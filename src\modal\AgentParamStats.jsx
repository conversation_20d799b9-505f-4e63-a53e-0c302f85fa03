import React, { useEffect, useState, useMemo } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useLazyGetAgentParamStatsQuery } from '../services/inference';
import { useTranslation } from 'react-i18next';
import { XAxis, YAxis, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import _ from 'lodash';


const AgentParamStats = (props) => {
  const { isOpened, setIsOpened, query } = props;

  const { t } = useTranslation();
  
  const [chartData, setChartData] = useState([]);

  const [trigger, { data }] = useLazyGetAgentParamStatsQuery();

  useEffect(() => {
    if (isOpened) {
      trigger(query);
    }
  }, [isOpened, query, trigger]);

  useEffect(() => {
    if (_.isEmpty(data)) return;

    const { ng_data_points: ng, ok_data_points: ok } = data;

    const allValues = [];

    ok.forEach((v) => {
      allValues.push({
        value: v,
        type: 'ok',
      });
    });

    ng.forEach((v) => {
      allValues.push({
        value: v,
        type: 'ng',
      });
    });

    const sortedValues = allValues.sort((a, b) => a.value - b.value);

    const result = [];

    let okIndex = 0;
    let ngIndex = 0;

    sortedValues.forEach((v) => {
      if (v.type === 'ok') {
        okIndex += 1;
      } else {
        ngIndex += 1;
      }

      const okCount = ok.length - okIndex;
      const ngCount = ngIndex;

      result.push({
        value: v.value,
        ok: okCount,
        ng: ngCount,
      });
    });

    setChartData(result);
  }, [data]);

  const recommendedInfo = useMemo(() => {
    if (_.isEmpty(chartData) || _.isEmpty(data)) return { value: null, range: null };

    const diffStart = chartData[0].ok - chartData[0].ng;
    const diffEnd = chartData[chartData.length - 1].ok - chartData[chartData.length - 1].ng;
    const hasIntersection = diffStart * diffEnd <= 0;

    if (hasIntersection) {
      const closest = chartData.reduce((prev, curr) => {
        return Math.abs(curr.ok - curr.ng) < Math.abs(prev.ok - prev.ng) ? curr : prev;
      });
      return { value: closest.value, range: null };
    }

    const okMax = _.max(data.ok_data_points);
    const ngMin = _.min(data.ng_data_points);
    return { value: null, range: [okMax, ngMin] };
  }, [chartData, data]);

  return (
    <CustomModal
      width={500}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%]'>
        {t('common.viewStats')}
      </span>}
      footer={null}
    >
      {!_.isEmpty(chartData) ?
        <div className='flex flex-col p-4 items-center justify-center'>
          <ResponsiveContainer width='100%' height={300}>
            <LineChart data={chartData}>
              <XAxis
                dataKey='value'
                label={{ value: t('common.aiDeviationScore'), position: 'insideBottom', offset: -5 }}
                tickFormatter={(v) => `${v.toFixed(4)}`}
                interval={0}
                includeHidden={true}
              />
              <YAxis
                domain={[0, Math.max(data?.ok_data_points.length || 0, data?.ng_data_points.length || 0)]}
                tickFormatter={(v) => `${v.toFixed(0)}`}
                label={{ value: t('common.count'), angle: -90, position: 'insideLeft', style: { textAnchor: 'middle' } }}
                allowDecimals={false}
              />
              <Tooltip formatter={(v) => `${v}`} />
              <Line type='monotone' dataKey='ok' stroke='green'  />
              <Line type='monotone' dataKey='ng' stroke='red' />
            </LineChart>
          </ResponsiveContainer>
            {/* {recommendedInfo.value !== null && (
              <div className='mt-2 text-center'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.recommendedThresholdValue')} {recommendedInfo.value.toFixed(2)}
                </span>
              </div>
            )}
            {_.isNumber(recommendedInfo, 'range.0', null) && _.isNumber(recommendedInfo, 'range.1', null) && (
              <div className='mt-2 text-center'>
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.recommendedThresholdValue')} {recommendedInfo.range[0].toFixed(2)} - {recommendedInfo.range[1].toFixed(2)}
                </span>
              </div>
            )} */}
        </div>
      :
        <div className='flex p-4 items-center justify-center'>
          <span className='font-source text-[12px] font-normal lead ing-[150%]'>
            {t('common.noData')}
          </span>
        </div>
      }
    </CustomModal>
  );
};

export default AgentParamStats;
