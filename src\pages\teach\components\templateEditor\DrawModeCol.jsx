import { Collapse, ConfigProvider, Dropdown, Tooltip } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import _ from 'lodash';


const DrawModeCol = (props) => {
  const {
    setIsDrawModeEnabled,
    selectedCid,
    allComponents,
    allFeatures,
    refetchAllComponents,
    refetchAllFeatures,
  } = props;

  const { t } = useTranslation();

  const collapseContRef = useRef(null);

  const [collapseHeight, setCollapseHeight] = useState(0);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [selectedFid, setSelectedFid] = useState(null);

  useEffect(() => {
    setSelectedComponent(_.find(allComponents, c => c.region_group_id === selectedCid));
  }, [selectedCid, allComponents]);

  useEffect(() => {
    console.log('collapseContRef.current.offsetHeight:', collapseContRef.current.offsetHeight);
    if (!collapseContRef.current) return;
    console.log('collapseContRef.current.offsetHeight:', collapseContRef.current.offsetHeight);
    setCollapseHeight(collapseContRef.current.offsetHeight);
  }, []);

  return (
    <div className='flex w-[348px] flex-col items-start self-stretch bg-[#ffffff0d]'>
      <div className='flex py-4 flex-col gap-2 self-stretch border-b-[1px] border-b-gray-2'>
        <div className='flex px-2 items-center self-stretch gap-2'>
          <div
            className='flex h-6 w-6 justify-center items-center hover:bg-[#ffffff0d] rounded-[4px] cursor-pointer transition-all duration-300 ease-in-out'
            onClick={() => setIsDrawModeEnabled(false)}
          >
            <img src='/icn/arrowLeft_white.svg' alt='arrowLeft' className='w-[7px] h-3' />
          </div>
          <span className='font-source text-[14px] font-semibold leading-[normal] pt-0.5'>
            {t('productDefine.exitDrawMode')}
          </span>
        </div>
      </div>
      <div
        className='flex p-0.5 gap-2 flex-1 self-stretch'
        ref={collapseContRef}
      >
        <div
          className='flex self-stretch w-full'
          style={{
            height: `${collapseHeight}px`,
            overflowY: 'auto',
          }}
        >
          {collapseHeight > 0 &&
            <ConfigProvider
              theme={{
                components: {
                  Collapse: {
                    headerPadding: '0px 0 0px 8px',
                    contentPadding: '0 0 0 8px',
                  }
                }
              }}
            >
              <CustomCollapse
                style={{ width: '100%' }}
                activeKey={'1'}
                items={[
                  {
                    key: '1',
                    label: <div className={`flex h-[32px] px-2 items-center gap-2 justify-between hover:bg-[#feb61733] transition-all duration-300 ease-in-out`}>
                      <Tooltip
                        title={<span className='font-source text-[12px] font-normal leading-[150%]'>{_.get(selectedComponent, 'package_no', '')}-{_.get(selectedComponent, 'region_group_id', 0)}</span>}
                        placement='right'
                      >
                        <div className='flex items-center gap-2 w-[210px] justify-between'>
                          <span className='font-source text-[12px] font-normal leading-[150%] overflow-hidden overflow-ellipsis whitespace-nowrap'>
                            {_.get(selectedComponent, 'package_no')}-{_.get(selectedComponent, 'region_group_id')}
                          </span>
                          {!_.find(allFeatures, f => f.group_id === selectedCid) &&
                            <span className='font-source text-[12px] font-normal leading-[150%]'>
                              {t('productDefine.missingROI')}
                            </span>
                          }
                        </div>
                      </Tooltip>
                      {/* <Dropdown
                        menu={{
                          items: [
                            {
                              key: 'deleteComponent',
                              label: <span className='font-source text-[12px] font-normal leading-[150%]'>{t('productDefine.deleteComponent')}</span>,
                              onClick: () => {
                                const run = async (productId, step, groupId) => {
                                  dispatch(setIsContainerLvlLoadingEnabled(true));

                                  const res = await deleteComponent({
                                    definition_product_id: productId,
                                    definition_step: step,
                                    region_group_id: groupId,
                                  });

                                  if (res.error) {
                                    console.error('deleteComponent error:', _.get(res, 'error.message', ''));
                                    aoiAlert(t('notification.error.deleteComponent'), ALERT_TYPES.COMMON_ERROR);
                                    dispatch(setIsContainerLvlLoadingEnabled(false));
                                    return;
                                  }

                                  await refetchAllComponents();
                                  await refetchAllFeatures();

                                  dispatch(setIsContainerLvlLoadingEnabled(false));
                                };

                                run(productId, 0, _.get(c, 'region_group_id', 0));
                              }
                            }
                          ]
                        }}
                      >
                        <div className='flex w-[18px] h-[18px] items-center justify-center rounded-[4px] hover:bg-[#ffffff0f] transition-all duration-300 ease-in-out'>
                          <img src='/icn/ellipsis_white.svg' alt='ellipsis' className='w-3 h-[2px]' />
                        </div>
                      </Dropdown> */}
                    </div>,
                    children: <div className='flex flex-col self-stretch'>
                      {_.map(_.filter(allFeatures, f => f.group_id === selectedCid), f => (
                        <div className={`pl-4 flex items-center gap-2 justify-between px-2 h-[32px] hover:bg-[#feb61733] transition-all duration-300 ease-in-out`}>
                          <Tooltip
                            title={<span className='font-source text-[12px] font-normal leading-[150%]'>{_.get(f, 'feature_type', '')}-{_.get(f, 'feature_id', 0)}</span>}
                            placement='right'
                          >
                            <span className='font-source text-[12px] font-normal leading-[150%] w-[200px] overflow-hidden overflow-ellipsis whitespace-nowrap'>
                              {_.get(f, 'feature_type', '')}-{_.get(f, 'feature_id', 0)}
                            </span>
                          </Tooltip>
                          {/* <Dropdown
                            menu={{
                              items: [
                                {
                                  key: 'deleteFeature',
                                  label: <span className='font-source text-[12px] font-normal leading-[150%]'>{t('productDefine.deleteFeature')}</span>,
                                  onClick: () => {
                                    const run = async (productId, step, featureId) => {
                                      dispatch(setIsContainerLvlLoadingEnabled(true));

                                      const res = await deleteFeature({
                                        product_id: productId,
                                        step: step,
                                        feature_id: featureId,
                                      });

                                      if (res.error) {
                                        console.error('deleteFeature error:', _.get(res, 'error.message', ''));
                                        aoiAlert(t('notification.error.deleteFeature'), ALERT_TYPES.COMMON_ERROR);
                                        dispatch(setIsContainerLvlLoadingEnabled(false));
                                        return;
                                      }

                                      if (selectedFid === featureId) {
                                        setSelectedFid(null);
                                      }

                                      await refetchAllFeatures();
                                      await refetchAllComponents();

                                      dispatch(setIsContainerLvlLoadingEnabled(false));
                                    };

                                    run(productId, 0, _.get(f, 'feature_id', 0));
                                  },
                                }
                              ]
                            }}
                          >
                            <div className='flex w-[18px] h-[18px] items-center justify-center rounded-[4px] hover:bg-[#ffffff0f] transition-all duration-300 ease-in-out'>
                              <img src='/icn/ellipsis_white.svg' alt='ellipsis' className='w-3 h-[2px]' />
                            </div>
                          </Dropdown> */}
                        </div>
                      ))}
                    </div>,
                  }
                ]}
              />
            </ConfigProvider>
          }
        </div>
      </div>
      <div className='flex p-3 flex-col gap-2.5 flex-1 self-stretch border-t-[1px] border-t-gray-2'>

      </div>
    </div>
  );
};

const CustomCollapse = styled(Collapse)`
  border: none !important;
  border-radius: 0 !important;
  background-color: transparent !important;
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  .ant-collapse-header[aria-expanded="true"] {
    background: rgba(86, 204, 242, 0.10) !important;
    color: #56CCF2 !important;
  }
  .ant-collapse-expand-icon {
    height: 32px !important;
  }
  .ant-collapse-header {
    &:hover {
      background: rgba(86, 204, 242, 0.10) !important;
    }
    border-bottom: 1px solid #ffffff0f !important;
  }
  .ant-collapse-content {
    border-radius: 0 !important;
    background-color: transparent !important;
  }
`;

export default DrawModeCol;