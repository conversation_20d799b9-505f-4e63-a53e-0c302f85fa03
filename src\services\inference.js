import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';
import _ from 'lodash';


export const inferenceApi = createApi({
  reducerPath: 'inferenceApi',
  baseQuery,
  tagTypes: ['Inference'],
  endpoints: (build) => ({
    startInferenceSession: build.mutation({
      query: (body) => ({
        url: '/inference',
        method: 'POST',
        body,
      }),
    }),
    // start inference then call this to enable continuous inference
    startContinuousInference: build.mutation({
      query: (body) => ({
        url: '/startContinuousRun',
        method: 'POST',
        body,
      }),
    }),
    stopContinuousInference: build.mutation({
      query: (body) => ({
        url: '/stopContinuousRun',
        method: 'POST',
        body,
      }),
    }),
    inferenceTrigger: build.mutation({
      query: (body) => ({
        url: '/inferenceTrigger',
        method: 'POST',
        body,
      }),
    }),
    stopInference: build.mutation({
      query: (params) => ({
        url: '/stopInference',
        method: 'POST',
        params,
      }),
    }),
    getInferenceStatus: build.query({
      query: () => '/inferenceStatus',
    }),
    lineItemResults: build.query({
      query: (params) => ({
        url: '/lineItemResults',
        method: 'GET',
        params,
      }),
    }),
    reevaluateLineItem: build.mutation({
      query: (body) => ({
        url: '/reevaluateLineItem',
        method: 'POST',
        body,
      }),
    }),
    setGolden: build.mutation({
      query: (body) => ({
        url: '/setGolden',
        method: 'PUT',
        body,
      }),
    }),
    getAllSessions: build.query({
      query: (query) => ({
        url: '/allSessions',
        method: 'GET',
        params: query,
      }),
      transformResponse: (response, meta) => {
        // include page count
        const headers = meta.response.headers;

        return {
          data: response,
          pageCount: headers.get('page-count'),
        };
      },
    }),
    deleteSession: build.mutation({
      query: (params) => ({
        url: '/session',
        method: 'DELETE',
        params
      }),
    }),
    getInspectedComponent: build.query({
      query: (params) => {
        if (!_.isInteger(Number(params.inspected_product_id))) throw new Error('Invalid ipc product id');
        return {
          url: '/inspected/component',
          method: 'GET',
          params,
        };
      },
    }),
    getInspectedFeature: build.query({
      query: (params) => {
        if (!_.isInteger(Number(params.inspected_product_id))) throw new Error('Invalid ipc product id');
        return {
          url: '/inspected/feature',
          method: 'GET',
          params,
        };
      },
    }),
    getInferenceStatus: build.query({
      query: () => {
        return {
          url: '/inferenceStatus',
          method: 'GET',
        };
      }
    }),
    getAllInspections: build.query({
      query: (query) => ({
        url: '/allInspections',
        method: 'GET',
        params: query,
      }),
      transformResponse: (response, meta) => {
        // include page count
        const headers = meta.response.headers;
        return {
          data: response,
          pageCount: headers.get('page-count'),
        };
      },
    }),
    enableContinuouslyInspection: build.mutation({
      query: (body) => ({
        url: '/enableContinuouslyInspection',
        method: 'POST',
        body,
      }),
    }),
    annotateFeature: build.mutation({
      query: (body) => ({
        url: `/annotate`,
        method: 'PUT',
        body,
      }),
    }),
    submitGoodFeedbackForAll: build.mutation({
      query: (body) => ({
        url: '/quickAnnotate',
        method: 'PUT',
        body,
      }),
    }),
    cancelFeedback: build.mutation({
      query: (body) => ({
        url: '/annotate',
        method: 'DELETE',
        body,
      }),
    }),
    deleteInspectionRecordByProductId: build.mutation({
      query: (product_id) => ({
        url: '/inspection',
        method: 'DELETE',
        params: { product_id },
      }),
    }),
    getLineItemResults: build.query({
      query: (params) => ({
        url: '/lineItemResults',
        method: 'GET',
        params,
      }),
    }),
    modelUpdateTrigger: build.mutation({
      query: (body) => ({
        url: '/modelUpdateTrigger',
        method: 'POST',
        body,
      }),
    }),
    registerFeatures: build.mutation({
      query: (body) => ({
        url: '/registerFeatures',
        method: 'POST',
        body,
      }),
    }),
    getDataSetExample: build.query({
      query: (params) => ({
        url: '/data_set/examples',
        method: 'GET',
        params,
      }),
    }),
    getNeighborInspections: build.query({
      query: (query) => ({
        url: '/locateInspection',
        method: 'GET',
        params: query,
      }),
    }),
    getSessionInfo: build.query({
      // get session info by ipc_session_id
      query: (ipc_session_id) => ({
        url: '/session',
        method: 'GET',
        params: { ipc_session_id: ipc_session_id },
      }),
    }),
    markTrainingExample: build.mutation({
      query: (body) => ({
        url: '/data_set/markTrainingExample',
        method: 'PUT',
        body,
      }),
    }),
    reevaluateExamples: build.mutation({
      query: (body) => ({
        url: '/data_set/examples/reevaluate',
        method: 'POST',
        body,
      }),
    }),
    exportInspectedFeature: build.query({
      query: (params) => ({
        url: '/exportInspectedFeature',
        method: 'GET',
        params,
      }),
    }),
    getReevaluationResult: build.query({
      query: (params) => ({
        url: '/data_set/features',
        method: 'GET',
        params,
      }),
    }),
    getAgentParamStats: build.query({
      query: (params) => ({
        url: '/data_set/examples/agent_param',
        method: 'GET',
        params,
      }),
    }),
    annotateGroup: build.mutation({
      query: (body) => ({
        url: '/annotateGroup',
        method: 'PUT',
        body,
      }),
    }),
    createComponentVariation: build.mutation({
      query: (body) => ({
        url: '/createComponentVariation',
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const {
  useLazyGetAllSessionsQuery,
  useGetAllSessionsQuery,
  useStartInferenceSessionMutation,
  useGetInspectedComponentQuery,
  useGetInspectedFeatureQuery,
  useStopInferenceMutation,
  useLazyGetInferenceStatusQuery,
  useInferenceTriggerMutation,
  useGetAllInspectionsQuery,
  useLazyGetAllInspectionsQuery,
  useAnnotateFeatureMutation,
  useSubmitGoodFeedbackForAllMutation,
  useCancelFeedbackMutation,
  useDeleteInspectionRecordByProductIdMutation,
  useLazyGetLineItemResultsQuery,
  useGetInferenceStatusQuery,
  useModelUpdateTriggerMutation,
  useRegisterFeaturesMutation,
  useLazyGetDataSetExampleQuery,
  useGetNeighborInspectionsQuery,
  useGetSessionInfoQuery,
  useMarkTrainingExampleMutation,
  useLazyGetInspectedComponentQuery,
  useReevaluateExamplesMutation,
  useGetDataSetExampleQuery,
  useStartContinuousInferenceMutation,
  useStopContinuousInferenceMutation,
  useLazyExportInspectedFeatureQuery,
  useDeleteSessionMutation,
  useLazyGetReevaluationResultQuery,
  useGetReevaluationResultQuery,
  useLazyGetAgentParamStatsQuery,
  useGetAgentParamStatsQuery,
  useAnnotateGroupMutation,
  useCreateComponentVariationMutation,
} = inferenceApi;