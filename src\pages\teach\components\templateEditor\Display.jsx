import React, { useEffect, useRef, useState } from 'react';
import { CustomSegmented } from '../../../../common/styledComponent';
import TemplateEditorViewer from '../../../../viewer/TemplateEditorViewer';
import { setCurrent3dSelectRectInfo } from '../../../../reducer/display';
import { ConfigProvider, Radio, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import _ from 'lodash';
import WindowedCroppedPointCloudDisplay from '../../recipe/fullCaputre/WindowCropped3DDisplay';
import { isAOI2DSMT } from '../../../../common/const';

const Display = (props) => {
	const {
		curProduct,
		allFeatures,
		selectedFid,
		selectedCid,
		selectedLineItemName,
		setSelectedFid,
		currentNewTemplateFromLibrary,
		setCurrentNewTemplateFromLibrary,
		fcanvasRef,
		allComponents,
		setSelectedCid,
		setSelectedLineItemName,
		refetchAllFeatures,
		refetchAllComponents,
		requiredLocateRect,
		selectedUngroupedFid,
		setSelectedUngroupedFid,
		setHoveredCid,
		setHoveredFid,
		setHoveredLineItemName,
		selectedAgentParam,
		setSelectedAgentParam,
		refetchCurProduct,
    updateAllFeaturesState,
    updateAllComponentsState,
    allFeaturesRef,
    allComponentsRef,
    mmToPixelRatio,
    refetchAllFeatureReevaluationResult,
    selectedArrayIndex,
    setSelectedArrayIndex,
    setSelectedFeatureType,
    setSelectedPartNo,
    setSelectedPackageNo,
    setSelectedScope,
    componentDetailWidth,
	} = props;

	const dispatch = useDispatch();

  const { t } = useTranslation();

  const viewerContainerRef = useRef(null);

        const [selectedTool, setSelectedTool] = useState('transform'); // transform
        const [threedViewerIdList, setThreedViewerIdList] = useState([]);

	const keyToToolMap = {
		'v': 'select',
		'h': 'transform',
		'p': 'select3DView',
		'1': 'addBody',
		'2': 'addSolder',
		'3': 'addICLead',
		'4': 'addText',
		'5': 'addBarcode'
	};

	// 键盘事件处理
	useEffect(() => {
		const handleKeyDown = (event) => {
			if (event.ctrlKey || event.altKey || event.metaKey) {
				return;
			}

			const activeElement = document.activeElement;
			if (activeElement && (
				activeElement.tagName === 'INPUT' ||
				activeElement.tagName === 'TEXTAREA' ||
				activeElement.contentEditable === 'true'
			)) {
				return;
			}

			const key = event.key.toLowerCase();
			const tool = keyToToolMap[key];

			if (tool) {
				if (tool === 'select3DView' && isAOI2DSMT) {
					return;
				}

				event.preventDefault();
				setSelectedTool(tool);
			}
		};

		document.addEventListener('keydown', handleKeyDown);

		return () => {
			document.removeEventListener('keydown', handleKeyDown);
		};
	}, [isAOI2DSMT]); // 依赖isAOI2DSMT，当它改变时重新绑定事件

	const handleSubmitSelect3DArea = async (pMin, pMax) => {
		const newProdRes = await refetchCurProduct();

		const curImageUri = _.get(newProdRes, 'data.inspectables[0].color_map_uri');
		const curDepthImgUri = _.get(
			newProdRes,
			'data.inspectables[0].depth_map_uri'
		);

		dispatch(
			setCurrent3dSelectRectInfo({
				pMin,
				pMax,
				curImageUri,
				curDepthImgUri,
			})
		);

		const id = _.uniqueId();
		setThreedViewerIdList( prev => [...prev, id]);
	};

  return (
    <div
      className='flex flex-col justify-center items-center self-stretch [background:rgba(0,0,0,0.80)] flex-1'
    >
      <div className='flex flex-col items-start self-stretch'>
        <div className='flex h-10 justify-center items-center gap-0.5 self-stretch [background:#292929] px-2 py-1'>

        </div>
        {/* <div className='flex items-center self-stretch [background:#292929] px-3 py-0'>
          <div className='flex items-center gap-2 flex-[1_0_0] px-0 py-1'>
            <span className='font-source text-xs font-normal leading-[150%] pt-[3px]'>
              {t('productDefine.onComponentMove')}
            </span>
            <ConfigProvider
              theme={{
                components: {
                  Radio: {
                    colorPrimary: '#2F80ED',
                  }
                }
              }}
            >
              <Radio.Group
                options={[
                  {
                    value: 'auto',
                    label: <span className='text-gray-6 font-source text-xs font-normal leading-[150%]'>
                      {t('productDefine.autoAdjustRois')}
                    </span>
                  },
                  {
                    value: 'manual',
                    label: <span className='text-gray-6 font-source text-xs font-normal leading-[150%]'>
                      {t('productDefine.keepInternalRoisPosition')}
                    </span>,
                  },
                ]}
              />
            </ConfigProvider>
          </div>
        </div> */}
      </div>
      <div className='relative w-full h-full'>
          {!isAOI2DSMT && _.map(threedViewerIdList, (id) => (
          <WindowedCroppedPointCloudDisplay
            key={id}
            id={id}
            selfUnmount={(id) => {
              setThreedViewerIdList(_.filter(threedViewerIdList, (threedViewerId) => threedViewerId !== id));
            }}
          />
        ))}
        {/* scene starts */}
      <div
          className='absolute top-0 left-0 w-full h-full'
          ref={viewerContainerRef}
        >
          <TemplateEditorViewer
            curProduct={curProduct}
            selectedTool={selectedTool}
            allFeatures={allFeatures}
            allComponents={allComponents}
            selectedFid={selectedFid}
            selectedCid={selectedCid}
            selectedLineItemName={selectedLineItemName}
            setSelectedFid={setSelectedFid}
            currentNewTemplateFromLibrary={currentNewTemplateFromLibrary}
            setCurrentNewTemplateFromLibrary={setCurrentNewTemplateFromLibrary}
            fcanvasRef={fcanvasRef}
            setSelectedCid={setSelectedCid}
            setSelectedLineItemName={setSelectedLineItemName}
            refetchAllFeatures={refetchAllFeatures}
            updateAllFeaturesState={updateAllFeaturesState}
            updateAllComponentsState={updateAllComponentsState}
            refetchAllComponents={refetchAllComponents}
            requiredLocateRect={requiredLocateRect}
            selectedUngroupedFid={selectedUngroupedFid}
            setSelectedUngroupedFid={setSelectedUngroupedFid}
            setHoveredCid={setHoveredCid}
            setHoveredFid={setHoveredFid}
            setHoveredLineItemName={setHoveredLineItemName}
            selectedAgentParam={selectedAgentParam}
            setSelectedAgentParam={setSelectedAgentParam}
            handleSubmitSelect3DArea={handleSubmitSelect3DArea}
            allFeaturesRef={allFeaturesRef}
            allComponentsRef={allComponentsRef}
            mmToPixelRatio={mmToPixelRatio}
            refetchAllFeatureReevaluationResult={refetchAllFeatureReevaluationResult}
            selectedArrayIndex={selectedArrayIndex}
            setSelectedArrayIndex={setSelectedArrayIndex}
            setSelectedFeatureType={setSelectedFeatureType}
            setSelectedPartNo={setSelectedPartNo}
            setSelectedPackageNo={setSelectedPackageNo}
            setSelectedScope={setSelectedScope}
            componentDetailWidth={componentDetailWidth}
          />
        </div>
        {/* scene ends */}
        {/* tools & controllers start */}
        <div
          className='absolute right-[8px] z-[20]'
          style={{ top: 'calc(50% - 117px)' }}
        >
          <CustomSegmented
            vertical
            value={selectedTool}
            onChange={(value) => {
              if (value === 'divider') return;
              setSelectedTool(value);
            }}
            options={[
              {
                value: 'select',
                label: <Tooltip
                  title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.select')} (V)</span>}
                  placement='left'
                >
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/navigator_white.svg'
                      alt='navigator'
                      className='w-3 h-3'
                    />
                  </div>
                </Tooltip>,
              },
              {
                value: 'transform',
                label: <Tooltip
                  title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('productDefine.panZoom')} (H)
                  </span>}
                  placement='left'
                >
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/backHand_white.svg'
                      alt='locator'
                      className='w-4 h-4'
                    />
                  </div>
                </Tooltip>,
              },
              ...(!isAOI2DSMT ? [{
                value: 'select3DView',
                label: <Tooltip
                  title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('productDefine.crop3DView')} (P)
                  </span>}
                  placement='left'
                >
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/viewIn3D_white.svg'
                      alt='viewIn3D'
                      className='w-4 h-4'
                    />
                  </div>
                </Tooltip>,
              }] : []),
              {
                value: 'divider',
                label: <div className='w-full h-[1px] bg-[#4F4F4F]' />,
                disabled: true,
              },
              {
                value: 'addBody',
                label: <Tooltip
                  title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('productDefine.addBodyFeature')} (1)
                  </span>}
                  placement='left'
                >
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/addBody_color.svg'
                      alt='addBody'
                      className='w-6 h-6'
                    />
                  </div>
                </Tooltip>,
              },
              {
                value: 'addSolder',
                label: <Tooltip
                  title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('productDefine.addSolderFeature')} (2)
                  </span>}
                  placement='left'
                >
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/addSolder_color.svg'
                      alt='addSolder'
                      className='w-6 h-6'
                    />
                  </div>
                </Tooltip>,
              },
              {
                value: 'addICLead',
                label: <Tooltip
                  title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('productDefine.addLeadFeature')} (3)
                  </span>}
                  placement='left'
                >
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/addICLead_color.png'
                      alt='addICLead'
                      className='w-6 h-6'
                    />
                  </div>
                </Tooltip>,
              },
              {
                value: 'addText',
                label: <Tooltip
                  title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('productDefine.addTextFeature')} (4)
                  </span>}
                  placement='left'
                >
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/addText_white.svg'
                      alt='addText'
                      className='w-6 h-[20px]'
                    />
                  </div>
                </Tooltip>,
              },
              {
                value: 'addBarcode',
                label: <Tooltip
                  title={<span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('productDefine.addBarcodeFeature')} (5)
                  </span>}
                  placement='left'
                >
                  <div className='flex w-8 h-8 justify-center items-center'>
                    <img
                      src='/icn/addBarcode_white.svg'
                      alt='addBarcode'
                      className='w-[20px] h-[20px]'
                    />
                  </div>
                </Tooltip>
              }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default Display;