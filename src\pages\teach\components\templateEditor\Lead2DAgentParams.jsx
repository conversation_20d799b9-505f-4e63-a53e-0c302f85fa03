import React from 'react';
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { Checkbox, Tooltip } from 'antd';
import { is2DLeadColorAgentParams, is2DLeadRangeAgentParams, twoDLeadGapAgentParamCheck } from '../../../../common/util';
import { isAOI2DSMT, leadFeatureType, leadGapFeatureType, leadInspection2D, lead2DV2AgentParamGroups } from '../../../../common/const';
import { text } from '../../../../common/translation';
import Lead2DColorRangeParams from './Lead2DColorRangeParams';
import AgentParamInt from './AgentParamInt';
import AgentParamFloat from './AgentParamFloat';
import AgentParamBool from './AgentParamBool';
import AgentParamRange from './AgentParamRange';

const Lead2DAgentParams = (props) => {
  const {
    lintItemName,
    selectedFeatureType,
    selectedGroupFeatureTypeAgentParams,
    setSelectedGroupFeatureTypeAgentParams,
    agentObj,
    updateAllFeaturesState,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    submit,
    isAgentParamStatsOpen,
    setIsAgentParamStatsOpen,
    statsQuery,
    setStatsQuery,
  } = props;

  const { t } = useTranslation();

  // Helper function to render a parameter with proper grouping
  const renderAgentParam = (agentParamName, id) => {
    // Skip if this is a color or range param (handled separately)
    if (is2DLeadColorAgentParams(lintItemName, agentParamName) ||
        is2DLeadRangeAgentParams(lintItemName, agentParamName)) {
      return null;
    }

    // Skip bridge_threshold for lead feature type
    if (selectedFeatureType === leadFeatureType && agentParamName === 'bridge_threshold') {
      return null;
    }

    // Skip if not applicable for gap feature type
    if (selectedFeatureType === leadGapFeatureType && !twoDLeadGapAgentParamCheck(lintItemName, agentParamName)) {
      return null;
    }

    return (
      <div
        className="grid self-stretch items-center gap-1"
        style={{ gridTemplateColumns: '8% 30% 62%' }}
        key={id}
      >
        <div className="flex items-center self-stretch">
          {!_.get(agentObj, `params.${agentParamName}.required`, false) && (
            <Checkbox
              checked={_.get(agentObj, `params.${agentParamName}.active`, false)}
              onClick={() => {
                let newGroupObj = _.cloneDeep(selectedGroupFeatureTypeAgentParams);
                newGroupObj = _.set(
                  newGroupObj,
                  `line_item_params.${lintItemName}.params.${agentParamName}.active`,
                  !_.get(agentObj, `params.${agentParamName}.active`, false)
                );

                submit(
                  newGroupObj,
                  lintItemName,
                  agentParamName,
                  selectedCid,
                  selectedPartNo,
                  selectedPackageNo,
                  selectedScope,
                  goldenProductId,
                  selectedFeatureType,
                );
              }}
            />
          )}
        </div>
        <div className="flex items-center self-stretch gap-2">
          <div className="flex items-center gap-1 w-full">
            <span
              className="font-source text-[12px] font-normal leading-[150%] pt-0.5 w-full overflow-hidden whitespace-nowrap text-ellipsis"
              title={t(`agentParamName.${agentObj.name}.${agentParamName}`)}
            >
              {t(`agentParamName.${agentObj.name}.${agentParamName}`)}
            </span>
            {!_.isEmpty(
              _.get(text, `agentParamDesc.${lintItemName}.${agentParamName}`)
            ) && (
              <Tooltip
                title={<span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t(`agentParamDesc.${lintItemName}.${agentParamName}`)}
                </span>}
              >
                <img
                  src="/icn/info_white.svg"
                  alt="info"
                  className="w-3 h-3"
                />
              </Tooltip>
            )}
          </div>
        </div>
        <div className='flex items-center self-stretch flex-1 gap-1'>
          {!_.isEmpty(_.get(agentObj, `params.${agentParamName}.param_range`, {})) && (
            <AgentParamRange
              fieldInfo={_.get(agentObj, `params.${agentParamName}.param_range`, {})}
              lintItemName={lintItemName}
              agentParamName={agentParamName}
              submit={submit}
              active={_.get(agentObj, `params.${agentParamName}.active`, false)}
              selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
              selectedCid={selectedCid}
              selectedPartNo={selectedPartNo}
              selectedPackageNo={selectedPackageNo}
              selectedScope={selectedScope}
              goldenProductId={goldenProductId}
              selectedFeatureType={selectedFeatureType}
            />
          )}
          {!_.isEmpty(_.get(agentObj, `params.${agentParamName}.param_int`, {})) && (
            <AgentParamInt
              fieldInfo={_.get(agentObj, `params.${agentParamName}.param_int`, {})}
              lintItemName={lintItemName}
              agentParamName={agentParamName}
              submit={submit}
              active={_.get(agentObj, `params.${agentParamName}.active`, false)}
              selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
              selectedCid={selectedCid}
              selectedPartNo={selectedPartNo}
              selectedPackageNo={selectedPackageNo}
              selectedScope={selectedScope}
              goldenProductId={goldenProductId}
              selectedFeatureType={selectedFeatureType}
            />
          )}
          {!_.isEmpty(_.get(agentObj, `params.${agentParamName}.param_float`, {})) && (
            <AgentParamFloat
              fieldInfo={_.get(agentObj, `params.${agentParamName}.param_float`, {})}
              lintItemName={lintItemName}
              agentParamName={agentParamName}
              submit={submit}
              active={_.get(agentObj, `params.${agentParamName}.active`, false)}
              selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
              selectedCid={selectedCid}
              selectedPartNo={selectedPartNo}
              selectedPackageNo={selectedPackageNo}
              selectedScope={selectedScope}
              goldenProductId={goldenProductId}
              selectedFeatureType={selectedFeatureType}
            />
          )}
          {_.isBoolean(_.get(agentObj, `params.${agentParamName}.param_bool`, false)) && (
            <AgentParamBool
              fieldInfo={_.get(agentObj, `params.${agentParamName}.param_bool`, false)}
              lintItemName={lintItemName}
              agentParamName={agentParamName}
              submit={submit}
              active={_.get(agentObj, `params.${agentParamName}.active`, false)}
              selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
              selectedCid={selectedCid}
              selectedPartNo={selectedPartNo}
              selectedPackageNo={selectedPackageNo}
              selectedScope={selectedScope}
              goldenProductId={goldenProductId}
              selectedFeatureType={selectedFeatureType}
            />
          )}
        </div>
      </div>
    );
  };

  // Get all available agent params and group them
  const allAgentParams = _.keys(_.get(agentObj, 'params', {}));

  // Group parameters by category
  const groupedParams = {
    common: [],
    solder: [],
    pad: [],
    tip: [],
    other: []
  };

  // Categorize each parameter
  allAgentParams.forEach(paramName => {
    let categorized = false;

    // Check each group
    Object.keys(lead2DV2AgentParamGroups).forEach(groupKey => {
      if (lead2DV2AgentParamGroups[groupKey].includes(paramName)) {
        groupedParams[groupKey].push(paramName);
        categorized = true;
      }
    });

    // If not categorized, put in 'other'
    if (!categorized) {
      groupedParams.other.push(paramName);
    }
  });

  const renderGroupSection = (groupName, params) => {
    if (params.length === 0) return null;

    return (
      <div key={groupName} className="flex flex-col gap-2">
        <div className="font-source text-[14px] font-semibold text-gray-700 capitalize">
          {groupName}
        </div>
        {params.map((paramName, index) => renderAgentParam(paramName, `${groupName}-${index}`))}
      </div>
    );
  };

  return (
    <div className="flex flex-1 flex-col self-stretch py-2 px-4 gap-4">
      {/* Render grouped parameters */}
      {renderGroupSection('common', groupedParams.common)}
      {renderGroupSection('solder', groupedParams.solder)}
      {renderGroupSection('pad', groupedParams.pad)}
      {renderGroupSection('tip', groupedParams.tip)}
      {renderGroupSection('other', groupedParams.other)}

      {/* Color Range Parameters */}
      {lintItemName === leadInspection2D && isAOI2DSMT && (
        <Lead2DColorRangeParams
          isAgentParamStatsOpen={isAgentParamStatsOpen}
          setIsAgentParamStatsOpen={setIsAgentParamStatsOpen}
          statsQuery={statsQuery}
          setStatsQuery={setStatsQuery}
          selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
          lintItemName={lintItemName}
          submit={submit}
          selectedCid={selectedCid}
          selectedPartNo={selectedPartNo}
          selectedPackageNo={selectedPackageNo}
          selectedScope={selectedScope}
          goldenProductId={goldenProductId}
          selectedFeatureType={selectedFeatureType}
          updateAllFeaturesState={updateAllFeaturesState}
          setSelectedGroupFeatureTypeAgentParams={setSelectedGroupFeatureTypeAgentParams}
        />
      )}
    </div>
  );
};

export default Lead2DAgentParams;