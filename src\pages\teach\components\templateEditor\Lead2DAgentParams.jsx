import React from 'react';
import _ from 'lodash';
import { is2DLeadColorAgentParams, is2DLeadRangeAgentParams, isMounting3DExtendedRoiAgent, isProfileRoiAgent, orderAgentParams, twoDLeadGapAgentParamCheck } from '../../../../common/util';
import { isAOI2DSMT, leadFeatureType, leadGapFeatureType, leadInspection2D } from '../../../../common/const';
import Lead2DColorRangeParams from './Lead2DColorRangeParams';

const Lead2DAgentParams = (props) => {
  const {
    lintItemName,
    selectedFeatureType,
    selectedGroupFeatureTypeAgentParams,
    setSelectedGroupFeatureTypeAgentParams,
    agentObj,
    updateAllFeaturesState,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    submit,
    isAgentParamStatsOpen,
    setIsAgentParamStatsOpen,
    statsQuery,
    setStatsQuery,
  } = props;

	return (
    <div
      className="flex flex-1 flex-col self-stretch py-2 px-4 gap-4"
    >
			{_.map(
				_.orderBy(
          _.keys(_.get(agentObj, 'params', {})),
          (o) => {
            // order by alphabetically
            // return t(`agentParamName.${agentObj.name}.${o}`);
            return orderAgentParams(o);
          }
        ),
				(agentParamName, id) => {
				if (
          !is2DLeadColorAgentParams(lintItemName, agentParamName) &&
					!is2DLeadRangeAgentParams(lintItemName, agentParamName) &&
					(selectedFeatureType === leadGapFeatureType ? twoDLeadGapAgentParamCheck(lintItemName, agentParamName) : true) &&
					(selectedFeatureType === leadFeatureType ? agentParamName !== 'bridge_threshold' : true)
				) {
					return (
            <>
            </>
          );
				}
			}
			)}
      {lintItemName === leadInspection2D && isAOI2DSMT &&
				<Lead2DColorRangeParams
					isAgentParamStatsOpen={isAgentParamStatsOpen}
					setIsAgentParamStatsOpen={setIsAgentParamStatsOpen}
					statsQuery={statsQuery}
					setStatsQuery={setStatsQuery}
          selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
					lintItemName={lintItemName}
					submit={submit}
          selectedCid={selectedCid}
          selectedPartNo={selectedPartNo}
          selectedPackageNo={selectedPackageNo}
          selectedScope={selectedScope}
          goldenProductId={goldenProductId}
          selectedFeatureType={selectedFeatureType}
          updateAllFeaturesState={updateAllFeaturesState}
          setSelectedGroupFeatureTypeAgentParams={setSelectedGroupFeatureTypeAgentParams}
				/>
			}
		</div>
	);
 uctId,
    submit,
    isAgentParamStatsOpen,
    setIsAgentParamStatsOpen,
    statsQuery,
    setStatsQuery,
  } = props;

	return (
    <div
      className="flex flex-1 flex-col self-stretch py-2 px-4 gap-4"
    >
			{_.map(
				_.orderBy(
          _.keys(_.get(agentObj, 'params', {})),
          (o) => {
            // order by alphabetically
            // return t(`agentParamName.${agentObj.name}.${o}`);
            return orderAgentParams(o);
          }
        ),
				(agentParamName, id) => {
				if (
          !is2DLeadColorAgentParams(lintItemName, agentParamName) &&
					!is2DLeadRangeAgentParams(lintItemName, agentParamName) &&
					(selectedFeatureType === leadGapFeatureType ? twoDLeadGapAgentParamCheck(lintItentParamName) : true) &&
					(selectedFeatureType === leadFeatureType ? agentParamName !== 'bridge_threshold' : true)
				) {
					return (
            <>
            </>
          );
				}
			}
			)}
      {lintItemName === leadInspection2D && isAOI2DSMT &&
				<Lead2DColorRangeParams
					isAgentParamStatsOpen={isAgentParamStatsOpen}
					setIsAgentParamStatsOpen={setIsAgentParamStatsOpen}
					statsQuery={statsQuery}
					setStatsQuery={setStatsQuery}
          selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
					lintItemName={lintItemName}
					submit={submit}
          selectedCid={selectedCid}
          selectedPartNo={selectedPartNo}
          selectedPackageNo={selectedPackageNo}
          selectedScope={selectedScope}
          goldenProductId={goldenProductId}
          selectedFeatureType={selectedFeatureType}
          updateAllFeat};

export default Lead2DAgentParams;