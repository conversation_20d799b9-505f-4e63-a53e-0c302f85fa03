import React from 'react';
import _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { InputNumber, Switch } from 'antd';
import { is2DLeadColorAgentParams, is2DLeadRangeAgentParams, twoDLeadGapAgentParamCheck } from '../../../../common/util';
import { isAOI2DSMT, leadFeatureType, leadGapFeatureType, leadInspection2D, lead2DV2AgentParamGroups, percentageAgentParamNames } from '../../../../common/const';
import Lead2DColorRangeParams from './Lead2DColorRangeParams';

const Lead2DAgentParams = (props) => {
  const {
    lintItemName,
    selectedFeatureType,
    selectedGroupFeatureTypeAgentParams,
    setSelectedGroupFeatureTypeAgentParams,
    agentObj,
    updateAllFeaturesState,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    submit,
    isAgentParamStatsOpen,
    setIsAgentParamStatsOpen,
    statsQuery,
    setStatsQuery,
  } = props;

  const { t } = useTranslation();

  // Helper function to get payload for parameter updates
  const getPayload = (value, agentParamName) => {
    let newParams = _.cloneDeep(selectedGroupFeatureTypeAgentParams);
    newParams = _.set(newParams, `${lintItemName}.params.${agentParamName}.value`, value);
    return newParams;
  };

  // Helper function to render a parameter input
  const renderAgentParam = (agentParamName) => {
    const paramObj = _.get(agentObj, `params.${agentParamName}`, {});
    const isActive = _.get(paramObj, 'active', false);
    const paramValue = _.get(paramObj, 'value', 0);
    const isPercentage = _.includes(percentageAgentParamNames, agentParamName);

    // Skip if this is a color or range param (handled separately)
    if (is2DLeadColorAgentParams(lintItemName, agentParamName) ||
        is2DLeadRangeAgentParams(lintItemName, agentParamName)) {
      return null;
    }

    // Skip bridge_threshold for lead feature type
    if (selectedFeatureType === leadFeatureType && agentParamName === 'bridge_threshold') {
      return null;
    }

    // Skip if not applicable for gap feature type
    if (selectedFeatureType === leadGapFeatureType && !twoDLeadGapAgentParamCheck(lintItemName, agentParamName)) {
      return null;
    }

    return (
      <div key={agentParamName} className="flex items-center self-stretch gap-2">
        {/* Enable/Disable Switch */}
        <Switch
          size="small"
          checked={isActive}
          onChange={(checked) => {
            let newParams = _.cloneDeep(selectedGroupFeatureTypeAgentParams);
            newParams = _.set(newParams, `${lintItemName}.params.${agentParamName}.active`, checked);
            setSelectedGroupFeatureTypeAgentParams(newParams);
            submit(newParams, lintItemName, agentParamName, selectedCid, selectedPartNo, selectedPackageNo, selectedScope, goldenProductId, selectedFeatureType);
          }}
        />

        {/* Parameter Name */}
        <div className="flex items-center gap-1 flex-1">
          <span
            className="font-source text-[12px] font-normal leading-[150%] whitespace-nowrap overflow-hidden text-ellipsis"
            title={t(`agentParamName.${lintItemName}.${agentParamName}`) || agentParamName}
          >
            {t(`agentParamName.${lintItemName}.${agentParamName}`) || agentParamName}
          </span>
        </div>

        {/* Parameter Input */}
        <div className="flex items-center gap-1">
          {_.isBoolean(_.get(paramObj, 'param_bool')) ? (
            <Switch
              size="small"
              disabled={!isActive}
              checked={paramValue}
              onChange={(checked) => {
                const payload = getPayload(checked, agentParamName);
                setSelectedGroupFeatureTypeAgentParams(payload);
                submit(payload, lintItemName, agentParamName, selectedCid, selectedPartNo, selectedPackageNo, selectedScope, goldenProductId, selectedFeatureType);
              }}
            />
          ) : _.isNumber(_.get(paramObj, 'param_int')) ? (
            <InputNumber
              size="small"
              disabled={!isActive}
              style={{ width: '80px' }}
              controls={false}
              min={_.get(paramObj, 'param_int.min', 0)}
              max={_.get(paramObj, 'param_int.max', 100)}
              value={isActive ? paramValue : null}
              onChange={(value) => {
                const payload = getPayload(value, agentParamName);
                setSelectedGroupFeatureTypeAgentParams(payload);
                submit(payload, lintItemName, agentParamName, selectedCid, selectedPartNo, selectedPackageNo, selectedScope, goldenProductId, selectedFeatureType);
              }}
            />
          ) : _.isNumber(_.get(paramObj, 'param_float')) ? (
            <>
              <InputNumber
                size="small"
                disabled={!isActive}
                style={{ width: '80px' }}
                controls={false}
                min={_.get(paramObj, 'param_float.min', 0)}
                max={_.get(paramObj, 'param_float.max', 100)}
                precision={2}
                value={isActive ? paramValue : null}
                onChange={(value) => {
                  const payload = getPayload(value, agentParamName);
                  setSelectedGroupFeatureTypeAgentParams(payload);
                  submit(payload, lintItemName, agentParamName, selectedCid, selectedPartNo, selectedPackageNo, selectedScope, goldenProductId, selectedFeatureType);
                }}
              />
              {isPercentage && (
                <span className="font-source text-[12px] font-normal text-gray-500">%</span>
              )}
            </>
          ) : null}
        </div>
      </div>
    );
  };

  // Get all available agent params
  const allAgentParams = _.keys(_.get(agentObj, 'params', {}));

  // Group parameters by category
  const groupedParams = {
    common: [],
    solder: [],
    pad: [],
    tip: [],
    other: []
  };

  // Categorize each parameter
  allAgentParams.forEach(paramName => {
    let categorized = false;

    // Check each group
    Object.keys(lead2DV2AgentParamGroups).forEach(groupKey => {
      if (lead2DV2AgentParamGroups[groupKey].includes(paramName)) {
        groupedParams[groupKey].push(paramName);
        categorized = true;
      }
    });

    // If not categorized, put in 'other'
    if (!categorized) {
      groupedParams.other.push(paramName);
    }
  });

  return (
    <div className="flex flex-1 flex-col self-stretch py-2 px-4 gap-4">
      {/* Common Parameters */}
      {groupedParams.common.length > 0 && (
        <div className="flex flex-col gap-2">
          <div className="font-source text-[14px] font-semibold text-gray-700">
            Common
          </div>
          {groupedParams.common.map(paramName => renderAgentParam(paramName))}
        </div>
      )}

      {/* Solder Parameters */}
      {groupedParams.solder.length > 0 && (
        <div className="flex flex-col gap-2">
          <div className="font-source text-[14px] font-semibold text-gray-700">
            Solder
          </div>
          {groupedParams.solder.map(paramName => renderAgentParam(paramName))}
        </div>
      )}

      {/* Pad Parameters */}
      {groupedParams.pad.length > 0 && (
        <div className="flex flex-col gap-2">
          <div className="font-source text-[14px] font-semibold text-gray-700">
            Pad
          </div>
          {groupedParams.pad.map(paramName => renderAgentParam(paramName))}
        </div>
      )}

      {/* Tip Parameters */}
      {groupedParams.tip.length > 0 && (
        <div className="flex flex-col gap-2">
          <div className="font-source text-[14px] font-semibold text-gray-700">
            Tip
          </div>
          {groupedParams.tip.map(paramName => renderAgentParam(paramName))}
        </div>
      )}

      {/* Other Parameters */}
      {groupedParams.other.length > 0 && (
        <div className="flex flex-col gap-2">
          <div className="font-source text-[14px] font-semibold text-gray-700">
            Other
          </div>
          {groupedParams.other.map(paramName => renderAgentParam(paramName))}
        </div>
      )}

      {/* Color Range Parameters */}
      {lintItemName === leadInspection2D && isAOI2DSMT && (
        <Lead2DColorRangeParams
          isAgentParamStatsOpen={isAgentParamStatsOpen}
          setIsAgentParamStatsOpen={setIsAgentParamStatsOpen}
          statsQuery={statsQuery}
          setStatsQuery={setStatsQuery}
          selectedGroupFeatureTypeAgentParams={selectedGroupFeatureTypeAgentParams}
          lintItemName={lintItemName}
          submit={submit}
          selectedCid={selectedCid}
          selectedPartNo={selectedPartNo}
          selectedPackageNo={selectedPackageNo}
          selectedScope={selectedScope}
          goldenProductId={goldenProductId}
          selectedFeatureType={selectedFeatureType}
          updateAllFeaturesState={updateAllFeaturesState}
          setSelectedGroupFeatureTypeAgentParams={setSelectedGroupFeatureTypeAgentParams}
        />
      )}
    </div>
  );
};

export default Lead2DAgentParams;