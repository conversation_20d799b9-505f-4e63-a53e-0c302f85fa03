import { thunk } from 'redux-thunk';
import { persistCombineReducers, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { configureStore, createListenerMiddleware, createStore } from '@reduxjs/toolkit';
import _ from 'lodash';
import createFilter, { createBlacklistFilter } from 'redux-persist-transform-filter';
import setting from './reducer/setting';
import display from './reducer/display';
import productDefine from './reducer/productDefine';
import { productApi } from './services/product';
import { inferenceApi } from './services/inference';
import { conveyorApi } from './services/conveyor';
import { cameraApi } from './services/camera';
import { systemApi } from './services/system';
import { accountApi } from './services/account';
// import displayReducer from './actions/display';
// import { cameraApi } from './services/camera'; 
// import { calibrationApi } from './services/calibration';


// ref: https://stackoverflow.com/questions/********/can-i-dispatch-an-action-in-reducer
// This middleware will just add the property "asyncDispatch - async dispatch" to all actions
const asyncDispatchMiddleware =
  ({ dispatch, getState }) =>
  (next) =>
  (action) => {
    let syncActivityFinished = false;
    let actionQueue = [];

    function flushQueue() {
      actionQueue.forEach((a) => dispatch(a)); // flush queue
      actionQueue = [];
    }

    function asyncDispatch(asyncAction) {
      actionQueue = actionQueue.concat([asyncAction]);

      if (syncActivityFinished) {
        flushQueue();
      }
    }

    const actionWithAsyncDispatch = Object.assign({}, action, {
      asyncDispatch,
    });

    const res = next(actionWithAsyncDispatch);

    syncActivityFinished = true;
    flushQueue();

    return res;
  };

// For user authentication local storage side effects
// const loginListenerMiddleware = createListenerMiddleware();
// loginListenerMiddleware.startListening({
//   matcher: authApi.endpoints.login.matchFulfilled,
//   effect: (action) => {
//     const {
//       payload: { email, token },
//     } = action;

//     if (!_.isEmpty(token) && !_.isEmpty(email)) {
//       localStorage.setItem('accessToken', `Bearer ${token}`);
//       localStorage.setItem('userEmail', email);
//       localStorage.setItem('accessTokenTimestamp', new Date().getTime());
//     }
//   },
// });

const middleware = [];
middleware.push(
  thunk,
  asyncDispatchMiddleware,
  productApi.middleware,
  inferenceApi.middleware,
  conveyorApi.middleware,
  cameraApi.middleware,
  systemApi.middleware,
  accountApi.middleware,
  // cameraApi.middleware,
  // calibrationApi.middleware,
);

// presist specific keys
const settingFilter = createFilter('setting', [
  'conveyorAccessToken',
  'ignoreConveyorReminderInProductDefine',
  'cameraAccessToken',
  'currentControlledConveyorSlotId',
]);

const persistConf = {
  key: 'root',
  storage,
  blacklist: [
    // 'camera',
    // 'product',
    // cameraApi.reducerPath,
    // calibrationApi.reducerPath,
    productApi.reducerPath,
    inferenceApi.reducerPath,
    conveyorApi.reducerPath,
    cameraApi.reducerPath,
    systemApi.reducerPath,
    accountApi.reducerPath,
    // 'setting',
    'display',
    'productDefine',
  ],
  transforms: [
    settingFilter,
  ],
};

const persistReducer = persistCombineReducers(persistConf, {
  // [cameraApi.reducerPath]: cameraApi.reducer,
  // [calibrationApi.reducerPath]: calibrationApi.reducer,
  [productApi.reducerPath]: productApi.reducer,
  [inferenceApi.reducerPath]: inferenceApi.reducer,
  [conveyorApi.reducerPath]: conveyorApi.reducer,
  [cameraApi.reducerPath]: cameraApi.reducer,
  [systemApi.reducerPath]: systemApi.reducer,
  [accountApi.reducerPath]: accountApi.reducer,
  setting: setting,
  display: display,
  productDefine: productDefine,
});

const store = configureStore({
  reducer: persistReducer,
  middleware: (getdefaultMiddleware) => {
      return getdefaultMiddleware({
        serializableCheck: false,
      }).concat([...middleware]);
    },
  devTools: true,
  preloadedState: undefined,
});

const persistor = persistStore(store);

export { store, persistor };