import { Segmented, Select, Tooltip } from 'antd';
import React, { Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { isAOI2DSMT, isAOI3DSMT } from '../../common/const';
import InspectionReviewFeatureViewer from '../../viewer/InspectionReviewFeatureViewer';
import _ from 'lodash';
import { CustomSegmented } from '../../common/styledComponent';
import HeightDiffComparison from './HeightDiffComparison';
import HeightDiffStacked from './HeightDiffStacked';


const InspectionReviewFeatureDetailRow = (props) => {
  const {
    displayDimensionMode,
    setDisplayDimensionMode,
    inspectedComponents,
    inspectedFeatures,
    selectedDCid,
    selectedRCid,
    selectedFid,
    selectedArrayIndex,
    maskImage,
    goldenComponents,
    goldenFeatures,
    isDepthMapDisplayed,
    setIsDepthMapDisplayed,
    pointCloudDisplayedView,
    setPointCloudDisplayedView,
    activeHeightDiffComparisonView,
    setActiveHeightDiffComparisonView,
    goldenProduct,
    isIpcCloudVisible,
    isGoldenCloudVisible,
    setIsIpcCloudVisible,
    setIsGoldenCloudVisible,
  } = props;

  const { t } = useTranslation();

  return (
    <div className='flex flex-col self-stretch flex-1'>
      {isAOI3DSMT &&
      <div className='flex justify-center items-center gap-0.5 self-stretch [background:#292929] px-2 py-0.5'>
        <div className='flex items-center gap-2 flex-[1_0_0] self-stretch rounded-[36px]'>
          <Tooltip
            title={
              <span className='font-source text-[12px] font-semibold leading-[150%]'>
                {t('review.display2DVisual')}
              </span>
            }
          >
            <div
              className={`flex w-8 h-8 justify-center items-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px]
                ${displayDimensionMode === '2d' ? 'bg-[#ffffff0d]' : ''}`}
              onClick={() => {
                setDisplayDimensionMode('2d');
              }}
            >
              <img
                src='/icn/full_color.svg'
                alt='fullColor'
                className='w-4 h-4'
              />
            </div>
          </Tooltip>
          {!isAOI2DSMT &&
            <Fragment>
              <div className='w-[1px] h-7 bg-gray-1' />
              <Tooltip
                title={
                  <span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('review.display3DVisual')}
                  </span>
                }
              >
                <div className={`flex w-8 h-8 justify-center items-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px]
                  ${displayDimensionMode === '3d' ? 'bg-[#ffffff0d]' : ''}`}
                  onClick={() => {
                    setDisplayDimensionMode('3d');
                  }}
                >
                  <img
                    src='/icn/viewIn3D_white.svg'
                    alt='viewIn3D'
                    className='w-4 h-4'
                  />
                </div>
              </Tooltip>
            </Fragment>
          }
          <div className='w-[1px] h-7 bg-gray-1' />
          {displayDimensionMode === '2d' && (
            <Fragment>
              <Tooltip
                title={
                  <span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('review.displayColorMap')}
                  </span>
                }
              >
                <div className={`flex w-8 h-8 justify-center items-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px]
                  ${!isDepthMapDisplayed ? 'bg-[#ffffff0d]' : ''}`}
                  onClick={() => {
                    setIsDepthMapDisplayed(false);
                  }}
                >
                  <img
                    src='/icn/full_color.svg'
                    alt='fullColor'
                    className='w-4 h-4'
                  />
                </div>
              </Tooltip>
              <div className='w-[1px] h-7 bg-gray-1' />
              <Tooltip
                title={
                  <span className='font-source text-[12px] font-semibold leading-[150%]'>
                    {t('review.displayDepthMap')}
                  </span>
                }
              >
                <div className={`flex w-8 h-8 justify-center items-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px]
                  ${isDepthMapDisplayed ? 'bg-[#ffffff0d]' : ''}`}
                  onClick={() => {
                    setIsDepthMapDisplayed(true);
                  }}
                >
                  <img
                    src='/icn/depth_map.svg'
                    alt='depthMap'
                    className='w-4 h-4'
                  />
                </div>
              </Tooltip>
            </Fragment>
          )}
          {displayDimensionMode === '3d' && (
            <Fragment>
              <CustomSegmented
                value={activeHeightDiffComparisonView}
                onChange={(value) => setActiveHeightDiffComparisonView(value)}
                options={[
                  {
                    label: <Tooltip
                      title={
                        <span className='font-source text-[12px] font-semibold leading-[150%]'>
                          {t('review.displayComparison')}
                        </span>
                      }
                    >
                      <div className='flex w-8 h-8 justify-center items-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px]'>
                        {/* { activeHeightDiffComparisonView === 'separate' ?
                          <img src='/img/icn/icn_separate_blue.svg' className='w-[13.8px] h-[14px] shrink' alt='separate' />
                          :
                          <img src='/img/icn/icn_separate_white.svg' className='w-[13.8px] h-[14px] shrink' alt='separate' />
                        } */}
                        <img src='/icn/icn_separate_white.svg' className='w-[13.8px] h-[14px] shrink' alt='separate' />
                      </div>
                    </Tooltip>,
                    value: 'comparison',
                  },
                  {
                    label: <Tooltip
                      title={
                        <span className='font-source text-[12px] font-semibold leading-[150%]'>
                          {t('review.displayOverlaid')}
                        </span>
                      }
                    >
                      <div className='flex w-8 h-8 justify-center items-center cursor-pointer hover:bg-[#ffffff0d] rounded-[4px]'>
                        {/* { activeHeightDiffComparisonView === 'stacked' ?
                          <img src='/img/icn/icn_overlay_blue.svg' className='w-[13.8px] h-[14px] shrink' alt='overlay' />
                          :
                          <img src='/img/icn/icn_overlay_white.svg' className='w-[13.8px] h-[14px] shrink' alt='overlay' />
                        } */}
                        <img src='/icn/icn_overlay_white.svg' className='w-[13.8px] h-[14px] shrink' alt='overlay' />
                      </div>
                    </Tooltip>,
                    value: 'overlaid',
                  }
                ]}
              />
              <Select
                popupMatchSelectWidth={false}
                style={{ width: '120px' }}
                value={pointCloudDisplayedView}
                onChange={(value) => setPointCloudDisplayedView(value)}
                options={[
                  // {
                  //   label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                  //     <img src='/img/icn/icn_perspective_white.svg' className='w-[12px] h-[12px] shrink' alt='perspective' />
                  //     <span className='font-source text-[12px] font-normal'>
                  //       {translation('viewInspection.perspective')}
                  //     </span>
                  //   </div>,
                  //   value: 'perspective',
                  // },
                  {
                    label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                      <img src='/icn/icn_topView_white.svg' className='w-[12px] h-[12px] shrink' alt='top' />
                      <span className='font-source text-[12px] font-normal'>
                        {t('review.top')}
                      </span>
                    </div>,
                    value: 'top',
                  },
                  {
                    label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                      <img src='/icn/icn_frontView_white.svg' className='w-[12px] h-[12px] shrink' alt='front' />
                      <span className='font-source text-[12px] font-normal'>
                        {t('review.front')}
                      </span>
                    </div>,
                    value: 'front',
                  },
                  {
                    label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                      <img src='/icn/icn_backView_white.svg' className='w-[12px] h-[12px] shrink' alt='back' />
                      <span className='font-source text-[12px] font-normal'>
                        {t('review.back')}
                      </span>
                    </div>,
                    value: 'back',
                  },
                  {
                    label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                      <img src='/icn/icn_leftView_white.svg' className='w-[12px] h-[12px] shrink' alt='left' />
                      <span className='font-source text-[12px] font-normal'>
                        {t('review.left')}
                      </span>
                    </div>,
                    value: 'left',
                  },
                  {
                    label: <div className='flex h-[26px] py-1 px-3 items-center gap-2 self-stretch'>
                      <img src='/icn/icn_rightView_white.svg' className='w-[12px] h-[12px] shrink' alt='right' />
                      <span className='font-source text-[12px] font-normal'>
                        {t('review.right')}
                      </span>
                    </div>,
                    value: 'right',
                  },
                ]}
              />
              <div className='flex gap-4 items-center self-stretch'>
                <Tooltip
                  title={
                    <span className='font-source text-[12px] font-semibold leading-[150%]'>
                      {t('review.toggleIpcCloudVisibility')}
                    </span>
                  }
                >
                  <div
                    className='flex gap-1 items-center hover:bg-gray-1 rounded-[2px] cursor-pointer self-stretch transition-all duration-300 px-2'
                    onClick={() => setIsIpcCloudVisible(!isIpcCloudVisible)}
                  >
                    <div
                      className='flex w-6 h-6 flex-col justify-center items-center'
                      onClick={() => setIsIpcCloudVisible(!isIpcCloudVisible)}
                    >
                      { isIpcCloudVisible ?
                      <img src='/icn/icn_visible_white.svg' className='w-[14px] h-[9.5px] shrink' alt='visible' />
                      :
                      <img src='/icn/icn_invisible_white.svg' className='w-[14px] h-[11.5px] shrink' alt='invisible' />
                      }
                    </div>
                    <div className='w-[14px] h-[14px] rounded-[2px] border-[1px] border-gray-3 bg-default-yellow' />
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('review.sample')}
                    </span>
                  </div>
                </Tooltip>
                <Tooltip
                  title={
                    <span className='font-source text-[12px] font-semibold leading-[150%]'>
                      {t('review.toggleGoldenCloudVisibility')}
                    </span>
                  }
                >
                  <div className='flex gap-1 items-center hover:bg-gray-1 rounded-[2px] cursor-pointer self-stretch transition-all duration-300 px-2'
                    onClick={() => setIsGoldenCloudVisible(!isGoldenCloudVisible)}
                  >
                    <div
                      className='flex w-6 h-6 flex-col justify-center items-center'
                      onClick={() => setIsGoldenCloudVisible(!isGoldenCloudVisible)}
                    >
                      { isGoldenCloudVisible ?
                      <img src='/icn/icn_visible_white.svg' className='w-[14px] h-[9.5px] shrink' alt='visible' />
                      :
                      <img src='/icn/icn_invisible_white.svg' className='w-[14px] h-[11.5px] shrink' alt='invisible' />
                      }
                    </div>
                    <div className='w-[14px] h-[14px] rounded-[2px] border-[1px] border-gray-3 bg-default-purple-2' />
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('review.golden')}
                    </span>
                  </div>
                </Tooltip>
              </div>
            </Fragment>
          )}
        </div>
      </div>
      }
      <div className='flex items-center gap-0.5 flex-[1_0_0] self-stretch'>
        {displayDimensionMode === '2d' && (
          <Fragment>
            <div className='flex flex-1 self-stretch rounded-[4px] border-[4px] border-[#F46D6D] bg-[#000]'>
              <InspectionReviewFeatureViewer
                componentInfo={_.find(inspectedComponents, c => c.result_component_id === selectedRCid)}
                featureInfo={_.find(inspectedFeatures, f => f.component_id === selectedRCid && f.feature_id === selectedFid)}
                isErroredSample={!_.get(_.find(inspectedFeatures, f => f.component_id === selectedRCid && f.feature_id === selectedFid), 'pass', false)}
                isInspectedView={true}
                maskImage={maskImage}
                isDepthMapDisplayed={isDepthMapDisplayed}
              />
            </div>
            <div className='flex flex-1 self-stretch rounded-[4px] border-[4px] border-[#81F499] bg-[#000]'>
              <InspectionReviewFeatureViewer
                componentInfo={_.find(goldenComponents, c => c.region_group_id === selectedDCid && c.array_index === selectedArrayIndex)}
                featureInfo={_.find(goldenFeatures, c => c.group_id === selectedDCid && c.feature_id === selectedFid && c.array_index === selectedArrayIndex)}
                isErroredSample={false}
                isInspectedView={false}
                isDepthMapDisplayed={isDepthMapDisplayed}
              />
            </div>
          </Fragment>
        )}
        {displayDimensionMode === '3d' && activeHeightDiffComparisonView === 'comparison' && (
          <HeightDiffComparison
            goldenFeatureInfo={_.find(goldenFeatures, c => c.group_id === selectedDCid && c.feature_id === selectedFid && c.array_index === selectedArrayIndex)}
            ipcFeatureInfo={_.find(inspectedFeatures, c => c.component_id === selectedRCid && c.feature_id === selectedFid)}
            goldenComponentInfo={_.find(goldenComponents, c => c.region_group_id === selectedDCid && c.array_index === selectedArrayIndex)}
            ipcComponentInfo={_.find(inspectedComponents, c => c.result_component_id === selectedRCid)}
            goldenProduct={goldenProduct}
            isIpcCloudVisible={isIpcCloudVisible}
            isGoldenCloudVisible={isGoldenCloudVisible}
            setIsIpcCloudVisible={setIsIpcCloudVisible}
            setIsGoldenCloudVisible={setIsGoldenCloudVisible}
            pointCloudDisplayedView={pointCloudDisplayedView}
          />
        )}
        { activeHeightDiffComparisonView === 'overlaid' && displayDimensionMode === '3d' && (
          <HeightDiffStacked
            goldenComponentInfo={_.find(goldenComponents, c => c.region_group_id === selectedDCid && c.array_index === selectedArrayIndex)}
            ipcComponentInfo={_.find(inspectedComponents, c => c.result_component_id === selectedRCid)}
            goldenFeatureInfo={_.find(goldenFeatures, c => c.group_id === selectedDCid && c.feature_id === selectedFid && c.array_index === selectedArrayIndex)}
            ipcFeatureInfo={_.find(inspectedFeatures, c => c.component_id === selectedRCid && c.feature_id === selectedFid)}
            goldenProduct={goldenProduct}
            isIpcCloudVisible={isIpcCloudVisible}
            isGoldenCloudVisible={isGoldenCloudVisible}
            setIsIpcCloudVisible={setIsIpcCloudVisible}
            setIsGoldenCloudVisible={setIsGoldenCloudVisible}
            pointCloudDisplayedView={pointCloudDisplayedView}
          />
        )}
      </div>
    </div>
  );
};

export default InspectionReviewFeatureDetailRow;