import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { InputNumber } from 'antd';

const AgentParamInt = (props) => {
	const {
		fieldInfo,
		lintItemName,
		selectedGroupFeatureTypeAgentParams,
		agentParamName,
		submit,
		active,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
	} = props;

	const [displayedValue, setDisplayedValue] = useState(
		_.get(fieldInfo, 'value', 0)
	);

  const displayedValRef = useRef(displayedValue);
	const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

	const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
		const payload = {
			...selectedGroupFeatureTypeAgentParams,
			line_item_params: {
				..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
				[lintItemName]: {
					..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
					params: {
						..._.get(
							selectedGroupFeatureTypeAgentParams,
							`line_item_params.${lintItemName}.params`
						),
						[agentParamName]: {
							..._.get(
								selectedGroupFeatureTypeAgentParams,
								`line_item_params.${lintItemName}.params.${agentParamName}`
							),
							param_int: {
								..._.get(
									selectedGroupFeatureTypeAgentParams,
									`line_item_params.${lintItemName}.params.${agentParamName}.param_int`
								),
								value: value,
							},
						},
					},
				},
			},
		};

		return payload;
	};

	useEffect(() => {
		setDisplayedValue(_.get(fieldInfo, 'value', 0));
    displayedValRef.current = _.get(fieldInfo, 'value', 0);
		selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
	}, [fieldInfo, selectedGroupFeatureTypeAgentParams]);

  useEffect(() => {
    return () => {
			if (displayedValRef.current !== _.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_int.value`, 0)) {
        const payload = getPayload(
          displayedValRef.current,
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    }
  }, []);

	return (
		<div className="flex flex-col self-stretch w-full">
			<InputNumber
				disabled={!active}
				step={1}
				style={{ width: '100%' }}
				controls={false}
				min={_.get(fieldInfo, 'min', 0)}
				max={_.get(fieldInfo, 'max', 1)}
				value={active ? displayedValue : null}
				onChange={(value) => {
					setDisplayedValue(value);
          displayedValRef.current = value;
				}}
				onBlur={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
				onPressEnter={(e) => {
					const val = Number(e.target.value);
					const payload = getPayload(
						val,
						selectedGroupFeatureTypeAgentParamsRef.current,
						lintItemName,
						agentParamName
					);
					submit(
            payload,
            lintItemName,
            agentParamName,
            selectedCid,
            selectedPartNo,
            selectedPackageNo,
            selectedScope,
            goldenProductId,
            selectedFeatureType,
          );
				}}
			/>
		</div>
	);
};

export default AgentParamInt;
