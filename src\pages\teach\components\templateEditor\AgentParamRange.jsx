import React, { useEffect, useRef, useState } from 'react';
import _ from 'lodash';
import { InputNumber } from 'antd';
import { useSelector } from 'react-redux';
import { systemApi } from '../../../../services/system';
import { mmValueDecimal, angleValueDecimal, mmAgentParamsNames, angleAgentParamsNames, percentageAgentParamNames } from '../../../../common/const';

const AgentParamRange = (props) => {
  const {
    fieldInfo,
    lintItemName,
    agentParamName,
    submit,
    active,
    selectedGroupFeatureTypeAgentParams,
    selectedCid,
    selectedPartNo,
    selectedPackageNo,
    selectedScope,
    goldenProductId,
    selectedFeatureType,
  } = props;

  const [displayedValue, setDisplayedValue] = useState(
    _.round(_.get(fieldInfo, 'ok_max', 0), 2)
  );

  const okMaxValRef = useRef(displayedValue);
  const selectedGroupFeatureTypeAgentParamsRef = useRef(selectedGroupFeatureTypeAgentParams);

  const { data: systemMetadata } = useSelector((state) =>
    systemApi.endpoints.getSystemMetadata.select()(state)
  );

  let decimal = _.get(
    systemMetadata,
    `default_line_items.${lintItemName}.params.${agentParamName}.param_range.ok_max`,
    0.01
  );

  const decimalStr = decimal.toString();

  if (decimalStr.indexOf('.') === -1) {
    decimal = 2;
  } else {
    decimal = decimalStr.split('.')[1].length;
  }
  decimal = Math.min(decimal, 2);

  if (_.includes(mmAgentParamsNames, `${lintItemName}.${agentParamName}`)) {
    decimal = mmValueDecimal;
  } else if (
    _.includes(angleAgentParamsNames, `${lintItemName}.${agentParamName}`)
  ) {
    decimal = angleValueDecimal;
  }

  const getPayload = (value, selectedGroupFeatureTypeAgentParams, lintItemName, agentParamName) => {
    const payload = {
      ...selectedGroupFeatureTypeAgentParams,
      line_item_params: {
        ..._.get(selectedGroupFeatureTypeAgentParams, 'line_item_params'),
        [lintItemName]: {
          ..._.get(selectedGroupFeatureTypeAgentParams, `line_item_params.${lintItemName}`),
          params: {
            ..._.get(
              selectedGroupFeatureTypeAgentParams,
              `line_item_params.${lintItemName}.params`
            ),
            [agentParamName]: {
              ..._.get(
                selectedGroupFeatureTypeAgentParams,
                `line_item_params.${lintItemName}.params.${agentParamName}`
              ),
              param_range: {
                ..._.get(
                  selectedGroupFeatureTypeAgentParams,
                  `line_item_params.${lintItemName}.params.${agentParamName}.param_range`
                ),
                ok_max: value,
              },
            },
          },
        },
      },
    };

    return payload;
  };

  useEffect(() => {
    setDisplayedValue(_.round(_.get(fieldInfo, 'ok_max', 0), 2));
    okMaxValRef.current = _.round(_.get(fieldInfo, 'ok_max', 0), 2);
    selectedGroupFeatureTypeAgentParamsRef.current = selectedGroupFeatureTypeAgentParams;
  }, [fieldInfo, selectedGroupFeatureTypeAgentParams]);

  useEffect(() => {
    return () => {
      if (okMaxValRef.current !== _.round(_.get(selectedGroupFeatureTypeAgentParamsRef.current, `line_item_params.${lintItemName}.params.${agentParamName}.param_range.ok_max`, 0), 2)) {
        const payload = getPayload(
          okMaxValRef.current,
          selectedGroupFeatureTypeAgentParamsRef.current,
          lintItemName,
          agentParamName
        );
        submit(
          payload,
          lintItemName,
          agentParamName,
          selectedCid,
          selectedPartNo,
          selectedPackageNo,
          selectedScope,
          goldenProductId,
          selectedFeatureType,
        );
      }
    }
  }, []);

  const isPercentage = _.includes(percentageAgentParamNames, agentParamName);

  return (
    <div className="flex flex-1 self-stretch w-full">
      <div className='flex flex-1 self-stretch items-center gap-1'>
        <InputNumber
          disabled={!active}
          style={{ width: '100%', height: '26px' }}
          controls={false}
          min={_.get(fieldInfo, 'min', 0)}
          max={_.get(fieldInfo, 'max', 1)}
          precision={decimal + 1}
          value={active ? displayedValue : null}
          onChange={(value) => {
            setDisplayedValue(value);
            okMaxValRef.current = value;
          }}
          onBlur={(e) => {
            const val = Number(e.target.value);
            const payload = getPayload(
              val,
              selectedGroupFeatureTypeAgentParamsRef.current,
              lintItemName,
              agentParamName
            );
            submit(
              payload,
              lintItemName,
              agentParamName,
              selectedCid,
              selectedPartNo,
              selectedPackageNo,
              selectedScope,
              goldenProductId,
              selectedFeatureType,
            );
          }}
          onPressEnter={(e) => {
            const val = Number(e.target.value);
            const payload = getPayload(
              val,
              selectedGroupFeatureTypeAgentParamsRef.current,
              lintItemName,
              agentParamName
            );
            submit(
              payload,
              lintItemName,
              agentParamName,
              selectedCid,
              selectedPartNo,
              selectedPackageNo,
              selectedScope,
              goldenProductId,
              selectedFeatureType,
            );
          }}
        />
        {isPercentage && (
          <span className="font-source text-[12px] font-normal text-gray-500">%</span>
        )}
      </div>
    </div>
  );
};

export default AgentParamRange;
