import React, { Fragment, useCallback, useEffect, useRef, useState } from 'react';
import { fabric } from 'fabric';
import { useGetImageMetaDataQuery } from '../services/camera';
import { useDispatch } from 'react-redux';
import { setTransparentLoadingEnabled } from '../reducer/setting';
import { loadHighResolScene, loadInitFullSizeThumbnail, zoomPanToObject } from './util';
import _ from 'lodash';
import { highResoluRefreshInterval } from '../common/const';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { useTranslation } from 'react-i18next';
import { featureROIInnerDimensionMaxLimit, newRectStrokeWidth } from '../common/const';
import { generalPanZoomMouseDownHandler, generalPanZoomMouseMoveHandler, generalPanZoomMouseUpHandler, generalPanZoomMouseWheelHandler, getTwoDRectPminPmax, middlePanZoomMouseDownHandler } from './util';
import { Button } from 'antd';
import ConfigAutoProgram from '../modal/ConfigAutoProgram';


const TemplateEditorRefineInspectionRegionViewer = (props) => {
  const {
    curProduct,
    refetchAllFeatures,
    refetchAllComponents,
    refetchAllFeatureReevaluationResult,
    setIsRedefiningInspectionRegion,
  } = props;
  
  const dispatch = useDispatch();

  const { t } = useTranslation();

  const canvasElRef = useRef(null);
  const viewerContRef = useRef(null);
  const fcanvasRef = useRef(null);
  const displayedHighResolSceneRef = useRef(null);
  const thumbnailBgSceneRef = useRef(null);
  const isPanningRef = useRef(false);
  const curDrawingRectRef = useRef(null);
  const drawingInitMousePos = useRef(null);

  const [drawingInfo, setDrawingInfo] = useState(null);
  const [curDrawnRectInfo, setCurDrawnRectInfo] = useState(null);
  const [isConfigAutoProgramOpened, setIsConfigAutoProgramOpened] = useState(false);

  const { data: curImageMetaData } = useGetImageMetaDataQuery({ uri: _.get(curProduct, 'inspectables[0].color_map_uri') });

  const updateZIndex = () => {
    if (!fcanvasRef.current) return;
    
    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1);
    if (displayedHighResolSceneRef.current) displayedHighResolSceneRef.current.moveTo(2);
    if (curDrawingRectRef.current) curDrawingRectRef.current.moveTo(3);

    fcanvasRef.current.renderAll();
  };

  const delayLoadHighSoluScene = useCallback(
    _.debounce(async ({
      fcanvasRef,
      rawImageW,
      rawImageH,
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    }) => {
      dispatch(setTransparentLoadingEnabled(true));

      await loadHighResolScene({
        fcanvasRef,
        rawImageW,
        rawImageH,
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
        callback: () => {
          updateZIndex();
        },
      });

      dispatch(setTransparentLoadingEnabled(false));
    }, highResoluRefreshInterval),
  [curImageMetaData]);

  const resetView = () => {
    if (!thumbnailBgSceneRef.current || !fcanvasRef.current) return;
    zoomPanToObject(thumbnailBgSceneRef.current, fcanvasRef.current, 0.01);
  };


  useEffect(() => {
    if (!curImageMetaData || !canvasElRef.current || !viewerContRef.current) return;

    if (!fcanvasRef.current) {
      fcanvasRef.current = new fabric.Canvas(canvasElRef.current, {
        antialias: 'off',
        uniformScaling: false,
        fireRightClick: true,
        stopContextMenu: true,
        fireMiddleClick: true,
      });
      
      fcanvasRef.current.setHeight(viewerContRef.current.offsetHeight);
      fcanvasRef.current.setWidth(viewerContRef.current.offsetWidth);
    }

    const attachEvents = (
      curImageMetadata,
      curProduct,
    ) => {
      fcanvasRef.current.on('mouse:down', (opt) => {
        if (opt.e.button === 1) {
          middlePanZoomMouseDownHandler(fcanvasRef, isPanningRef);
        } else if (opt.e.button === 0) {
          const pointer = fcanvasRef.current.getPointer(opt.e);
          curDrawingRectRef.current = new fabric.Rect({
            left: pointer.x,
            top: pointer.y,
            width: 0,
            height: 0,
            fill: 'transparent',
            stroke: 'red',
            strokeWidth: 10,
            selectable: false,
            strokeUniform: true, // Ensure stroke width remains consistent when scaling
            evented: false,
          });

          const mousePos = fcanvasRef.current.getPointer(opt.e, true);

          setDrawingInfo({
            curRectInnerWidth: 0,
            curRectInnerHeight: 0,
            curMouseTop: mousePos.y,
            curMouseLeft: mousePos.x,
          });
          drawingInitMousePos.current = {
            x: pointer.x,
            y: pointer.y,
          };

          fcanvasRef.current.add(curDrawingRectRef.current);

          updateZIndex();
        }
      });

      fcanvasRef.current.on('mouse:move', (opt) => {
        if (isPanningRef.current) {
          generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanningRef);
          return;
        }

        if (!curDrawingRectRef.current || _.isEmpty(drawingInitMousePos.current)) return;
        const pointer = fcanvasRef.current.getPointer(opt.e);
        const mousePos = fcanvasRef.current.getPointer(opt.e, true);

        // NOTE: keep rect's width and height positive when drawing and adjust the top left
        // ow the rect's left and right will be hidden for some reason
        if (_.get(drawingInitMousePos.current, 'x') > pointer.x) {
          curDrawingRectRef.current.set({
            left: pointer.x,
            width: drawingInitMousePos.current.x - pointer.x,
          })
        } else {
          curDrawingRectRef.current.set({
            width: pointer.x - drawingInitMousePos.current.x,
            left: drawingInitMousePos.current.x,
          });
        }

        if (_.get(drawingInitMousePos.current, 'y') > pointer.y) {
          curDrawingRectRef.current.set({
            top: pointer.y,
            height: drawingInitMousePos.current.y - pointer.y,
          });
        } else {
          curDrawingRectRef.current.set({
            height: pointer.y - drawingInitMousePos.current.y,
            top: drawingInitMousePos.current.y,
          });
        }

        updateZIndex();

        const curInnerWidth = curDrawingRectRef.current.width - newRectStrokeWidth;
        const curInnerHeight = curDrawingRectRef.current.height - newRectStrokeWidth;

        setDrawingInfo({
          curRectInnerWidth: curInnerWidth >= 0 ? curInnerWidth : -curInnerWidth,
          curRectInnerHeight: curInnerHeight >= 0 ? curInnerHeight : -curInnerHeight,
          curMouseTop: mousePos.y,
          curMouseLeft: mousePos.x,
        });
      });

      fcanvasRef.current.on('mouse:up', () => {
        if (isPanningRef.current) {
          generalPanZoomMouseUpHandler(fcanvasRef, isPanningRef);
          delayLoadHighSoluScene({
            fcanvasRef: fcanvasRef,
            rawImageW: _.get(curImageMetadata, 'width', 0),
            rawImageH: _.get(curImageMetadata, 'height', 0),
            displayedHighResolSceneRef,
            imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
            depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
          });
          return;
        }

        if (curDrawingRectRef.current.width === 0 || curDrawingRectRef.current.height === 0) {
          fcanvasRef.current.remove(curDrawingRectRef.current);
          curDrawingRectRef.current = null;
          return;
        }

        if (!curDrawingRectRef.current) return;

        if (curDrawingRectRef.current.width < 0) {
          curDrawingRectRef.current.left += curDrawingRectRef.current.width;
          curDrawingRectRef.current.width *= -1;
        }
        if (curDrawingRectRef.current.height < 0) {
          curDrawingRectRef.current.top += curDrawingRectRef.current.height;
          curDrawingRectRef.current.height *= -1;
        }

        updateZIndex();
        curDrawingRectRef.current.setCoords();

        const { pMax, pMin } = getTwoDRectPminPmax(curDrawingRectRef.current, curDrawingRectRef.current.strokeWidth);

        setCurDrawnRectInfo({
          pMin,
          pMax,
        });

        // fcanvasRef.current.remove(curDrawingRectRef.current);
        // curDrawingRectRef.current = null;
        setDrawingInfo(null);
        drawingInitMousePos.current = null;
      });

      fcanvasRef.current.on('mouse:wheel', (opt) => {
        generalPanZoomMouseWheelHandler(opt, fcanvasRef, isPanningRef);
        delayLoadHighSoluScene({
          fcanvasRef: fcanvasRef,
          rawImageW: _.get(curImageMetadata, 'width', 0),
          rawImageH: _.get(curImageMetadata, 'height', 0),
          displayedHighResolSceneRef,
          imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
          depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
        });
      });
    };

    const loadScene = async (
      curProduct,
      curImageMetadata,
      thumbnailBgSceneRef,
      displayedHighResolSceneRef,
    ) => {
      await loadInitFullSizeThumbnail({
        fcanvas: fcanvasRef.current,
        rawWidth: _.get(curImageMetadata, 'width'),
        rawHeight: _.get(curImageMetadata, 'height'),
        thumbnailBgSceneRef,
        imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
        depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
        type: 'image',
      });

      await loadHighResolScene({
        fcanvasRef,
        rawImageH: _.get(curImageMetadata, 'height', 0),
        rawImageW: _.get(curImageMetadata, 'width', 0),
        displayedHighResolSceneRef,
        imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
        depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
        type: 'image',
      });

      resetView();
    };

    loadScene(
      curProduct,
      curImageMetaData,
      thumbnailBgSceneRef,
      displayedHighResolSceneRef,
    );

    attachEvents(curImageMetaData, curProduct);

    const handleResize = () => {
      if (!fcanvasRef.current || !viewerContRef.current) return;

      fcanvasRef.current.setHeight(viewerContRef.current.offsetHeight);
      fcanvasRef.current.setWidth(viewerContRef.current.offsetWidth);
    };

    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(viewerContRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <Fragment>
      <ConfigAutoProgram
        onFinish={async () => {
          await refetchAllComponents();
          await refetchAllFeatures();
          await refetchAllFeatureReevaluationResult();
          setIsRedefiningInspectionRegion(false);
        }}
        isOpened={isConfigAutoProgramOpened}
        setIsOpened={setIsConfigAutoProgramOpened}
        productId={Number(_.get(curProduct, 'product_id'))}
        roi={curDrawnRectInfo}
        step={0}
        isFullAutoProgram={true}
      />
      <div className='relative w-full h-full'>
        {!_.isEmpty(curDrawnRectInfo) &&
          <div className='absolute top-8 left-[50%] translate-x-[-50%] z-[20]'>
            <div className='flex items-center gap-2 p-2 rounded-[4px] bg-[#ffffff0d]'>
              <Button
                onClick={() => {
                  setIsConfigAutoProgramOpened(true);
                }}
              >
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.confirm')}
                </span>
              </Button>
              <Button
                onClick={() => {
                  // remove curDrawingRect from scene
                  if (!_.isEmpty(curDrawingRectRef.current)) {
                    fcanvasRef.current.remove(curDrawingRectRef.current);
                    curDrawingRectRef.current = null;
                    setDrawingInfo(null);
                    drawingInitMousePos.current = null;
                    setCurDrawnRectInfo(null);
                  }
                }}
              >
                <span className='font-source text-[12px] font-normal leading-[150%]'>
                  {t('common.cancel')}
                </span>
              </Button>
            </div>
          </div>
        }
        <div
          className='absolute top-0 left-0 w-full h-full z-[10]'
          ref={viewerContRef}
        >
          <canvas
            ref={canvasElRef}
          />
        </div>
      </div>
    </Fragment>
  );
};

export default TemplateEditorRefineInspectionRegionViewer;