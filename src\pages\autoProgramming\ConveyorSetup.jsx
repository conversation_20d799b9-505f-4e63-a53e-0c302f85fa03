import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import ConveyorSelector from '../../components/ConveyorSelector';
import { useDispatch, useSelector } from 'react-redux';
import { GreenDefaultButtonConfigProvider, GreenPrimaryButtonConfigProvider } from '../../common/styledComponent';
import { Button, InputNumber } from 'antd';
import _ from 'lodash';
import { getCurrentConveyorStatus } from '../../common/util';
import { useAcquireConveyorControlMutation, useLazyGetAllConveyorStatusQuery, useReleaseConveyorControlMutation, useSubmitConveyorOperationMutation } from '../../services/conveyor';
import { useLazyGetInferenceStatusQuery } from '../../services/inference';
import { ALERT_TYPES, aoiAlert } from '../../common/alert';
import { useAcquireCameraControlMutation, useReleaseCameraControlMutation } from '../../services/camera';
import { setCameraAccessToken, setConveyorAccessToken, setCurrentControlledConveyorSlotId, setIsProgrammingUsingConveyor } from '../../reducer/setting';
import { conveyorOperation, conveyorUsage, fieldConstraints } from '../../common/const';


const ConveyorSetup = (props) => {
  const {
    curProduct,
    conveyorWidth,
    setConveyorWidth,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const [selectedConveyorSlotId, setSelectedConveyorSlotId] = useState(null);

  const [lazyGetConveyorStatus] = useLazyGetAllConveyorStatusQuery();
  const [lazyGetInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [acquireConveyorControl] = useAcquireConveyorControlMutation();
  const [acquireCameraControl] = useAcquireCameraControlMutation();
  const [releaseConveyorControl] = useReleaseConveyorControlMutation();
  const [releaseCameraControl] = useReleaseCameraControlMutation();
  const [submitConveyorOperation] = useSubmitConveyorOperationMutation();

  const conveyorAccessToken = useSelector((state) => state.setting.conveyorAccessToken);
  const cameraAccessToken = useSelector((state) => state.setting.cameraAccessToken);

  const handleLockConveyorSubmit = async (selectedConveyorSlot, productId) => {
    if (_.isEmpty(selectedConveyorSlot)) {
      aoiAlert(t('notification.error.pleaseSelectAConveyor'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const conveyorStatus = await getCurrentConveyorStatus(
      lazyGetConveyorStatus,
      lazyGetInferenceStatus,
      t,
    );

    // if any conveyor is in use
    for(const slotId of _.keys(conveyorStatus)) {
      if (_.get(conveyorStatus, `${slotId}.taskType`, '') !== 'none') {
        aoiAlert(t('notification.error.conveyorInUsedWhenTeach'), ALERT_TYPES.COMMON_ERROR);
        return;
      }
    }
    
    const res = await acquireConveyorControl({
      slot_id: Number(selectedConveyorSlot),
      product_id: Number(productId),
      intension: conveyorUsage.program,
    });

    if (res.error) {
      aoiAlert(t('notification.error.acquireConveyorControl'), ALERT_TYPES.COMMON_ERROR);
      console.error('acquireConveyorControl error:', _.get(res, 'error.message', ''));
      return;
    }

    dispatch(setConveyorAccessToken(_.get(res, 'data.conveyor_access_token', '')));
    dispatch(setIsProgrammingUsingConveyor(true));
    dispatch(setCurrentControlledConveyorSlotId(selectedConveyorSlot));

    const acquireCameraRes = await acquireCameraControl({
      slot_id: Number(selectedConveyorSlot),
      // slot_id: 1,
      conveyor_access_token: _.get(res, 'data.conveyor_access_token', ''),
    });

    if (res.error) {
      aoiAlert(t('notification.error.acquireCameraControl'), ALERT_TYPES.COMMON_ERROR);
      console.error('acquireCameraControl error:', _.get(acquireCameraRes, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.acquireConveyorControl'), ALERT_TYPES.COMMON_INFO);
    dispatch(setCameraAccessToken(_.get(acquireCameraRes, 'data.camera_access_token', '')));
  };

  const handleReleaseConveyorSubmit = async (selectedConveyorSlot, conveyorToken, cameraToken) => {
    if (_.isEmpty(selectedConveyorSlot)) return;

    // check if it's already released
    const conveStatusRes = await lazyGetConveyorStatus();

    if (conveStatusRes.error) {
      aoiAlert(t('notification.error.getAllConveyorStatus'), ALERT_TYPES.COMMON_ERROR);
      console.error('getAllConveyorStatus error:', _.get(conveStatusRes, 'error.message', ''));
      return;
    }

    if (_.get(conveStatusRes, `data.conveyor_map.${selectedConveyorSlot}.available`, false)) {
      aoiAlert(t('notification.error.conveyorAlreadyReleased'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await releaseConveyorControl(conveyorToken);

    if (res.error) {
      aoiAlert(t('notification.error.releaseConveyorControl'), ALERT_TYPES.COMMON_ERROR);
      console.error('releaseConveyorControl error:', _.get(res, 'error.message', ''));
      return;
    }

    const releaseCameraRes = await releaseCameraControl(cameraToken);

    if (releaseCameraRes.error) {
      aoiAlert(t('notification.error.releaseCameraControl'), ALERT_TYPES.COMMON_ERROR);
      console.error('releaseCameraControl error:', _.get(releaseCameraRes, 'error.message', ''));
      return;
    }

    aoiAlert(t('notification.success.releaseConveyorControl'), ALERT_TYPES.COMMON_INFO);
    dispatch(setConveyorAccessToken(''));
    // setSelectedConveyorSlot('');
    dispatch(setIsProgrammingUsingConveyor(false));
    dispatch(setCurrentControlledConveyorSlotId(null));
    dispatch(setCameraAccessToken(''));
  };

  const handleSubmitConveyorOperation = async (accessToken, operation) => {
    if (_.isEmpty(accessToken)) {
      aoiAlert(t('notification.error.conveyorAccessTokenEmpty'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const res = await submitConveyorOperation({
      conveyor_access_token: accessToken,
      op: operation,
    });

    if (res.error) {
      aoiAlert(t('notification.error.conveyorOperation'), ALERT_TYPES.COMMON_ERROR);
      console.error('submitConveyorOperation error:', _.get(res, 'error.message', ''));
      return;
    }
  };

  return (
    <div className='flex gap-0.5 flex-1 self-stretch'>
      <div className='flex flex-1 self-stretch bg-[#000]'>

      </div>
      <div className='flex w-[293px] flex-col self-stretch bg-[#ffffff05]'>
        <div className='flex flex-col p-4 gap-8 flex-1 self-stretch'>
          <div className='flex flex-col gap-1 self-stretch py-2 self-stretch'>
            <span className='font-source text-[16px] font-normal leading-[150%]'>
              {t('autoProgramming.placePCBAOnConveyor')}
            </span>
            <span className='font-source text-[12px] font-normal leading-[150%] text-gray-6 flex-wrap'>
              {t('productDefine.conveyorSetupDescPart1')}
              <img
                src='/icn/pcbIn_color.svg'
                alt='pcbIn'
                className='w-[22px] h-[14px] inline-block vertical-middle px-1'
              />
              {t('productDefine.conveyorSetupDescPart2')}
            </span>
          </div>
          <div className='flex flex-col gap-2 self-stretch'>
            <ConveyorSelector
              disabled={!_.isEmpty(conveyorAccessToken)}
              selectedConveyorSlotId={selectedConveyorSlotId}
              setSelectedConveyorSlotId={setSelectedConveyorSlotId}
            />
            <GreenPrimaryButtonConfigProvider>
              <Button
                type='primary'
                onClick={() => {
                  handleLockConveyorSubmit(
                    selectedConveyorSlotId,
                    Number(curProduct.product_id)
                  );
                }}
                disabled={!_.isEmpty(conveyorAccessToken)}
              >
                <div className='flex items-center gap-2'>
                  <img src='/icn/locked_black.svg' alt='locked' className='w-[9px] h-[12px]' />
                  <span className='font-source text-[12px] font-semibold leading-[150%] pt-0.5'>
                    {t('productDefine.lockConveyorForThisTask')}
                  </span> 
                </div>
              </Button>
            </GreenPrimaryButtonConfigProvider>
            <GreenDefaultButtonConfigProvider>
              <Button
                style={{ width: '100%' }}
                onClick={() => {
                  handleReleaseConveyorSubmit(
                    selectedConveyorSlotId,
                    conveyorAccessToken,
                    cameraAccessToken,
                  );
                }}
                disabled={_.isEmpty(conveyorAccessToken)}
              >
                <div className='flex items-center gap-2'>
                  <img src='/icn/unlock_white.svg' alt='unlock' className='w-[9px] h-[12px]' />
                  <span className='font-source text-[12px] font-normal leading-[normal]'>
                    {t('productDefine.releaseConveyor')}
                  </span>
                </div>
              </Button>
            </GreenDefaultButtonConfigProvider>
          </div>
          <div className='flex py-1.5 flex-col gap-1 self-stretch'>
            <div className='flex py-1 gap-2 self-stretch items-end'>
              <span className='font-source text-[14px] font-normal leading-[150%]'>
                {t('autoProgramming.conveyorWidth')}
              </span>
              <span className='font-source text-[12px] font-normal leading-[150%]'>
                {t('autoProgramming.limitTo')}
              </span>
            </div>
            <div className='flex py-1 items-center self-stretch gap-1'>
              <InputNumber
                min={fieldConstraints.productDefine.conveyorWidth.min}
                max={fieldConstraints.productDefine.conveyorWidth.max}
                value={conveyorWidth}
                onChange={(value) => setConveyorWidth(value)}
                controls={false}
                style={{ width: '100%' }}
              />
            </div>
          </div>
          <div className='flex flex-col gap-2 self-stretch'>
            <Button
              onClick={() => {
                handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.load);
              }}
            >
              <div className='flex items-center gap-2'>
                <img
                  src='/icn/pcbIn_color.svg'
                  alt='pcbIn'
                  className='w-[14px] h-[14px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                  {t('productDefine.PCBIn')}
                </span>
              </div>
            </Button>
            <Button
              onClick={() => {
                handleSubmitConveyorOperation(conveyorAccessToken, conveyorOperation.unload);
              }}
            >
              <div className='flex items-center gap-2'>
                <img
                  src='/icn/pcbOut_color.svg'
                  alt='pcbOut'
                  className='w-[14px] h-[14px]'
                />
                <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                  {t('productDefine.PCBEject')}
                </span>
              </div>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConveyorSetup;