import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './api';
import { localStorageKeys, serverHost } from '../common/const';
import _ from 'lodash';


export const productApi = createApi({
  reducerPath: 'productApi',
  baseQuery,
  tagTypes: ['Product'],
  endpoints: (build) => ({
    productInspectablesRegister: build.mutation({
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        try {
          // manually encode the variant
          const encodedVar = encodeURIComponent(arg.variant);
          const response = await fetch(`${serverHost}/registerInspectables?product_id=${arg.product_id}&variant=${encodedVar}&conveyor_access_token=${arg.conveyor_access_token}`, {
          // const response = await fetch(`${serverHost}/registerInspectables?product_id=${arg.product_id}`, {
            method: 'POST',
            headers: {
              'Authorization': localStorage.getItem(localStorageKeys.accessToken) || '',
            }
          });

          if (!response.ok) {
            return { error: { status: response.status, message: response.statusText } };
          }

          const data = await response.json();
          return { data };
        } catch (error) {
          return { error: { status: 'FETCH_ERROR', message: error.message } };
        }
      },
    }),
    getAllProducts: build.query({
      query: () => '/product/allProducts',
      providesTags: ['Product'],
    }),
    getProductById: build.query({
      query: (id) => {
        if (_.isNull(id) || !_.isInteger(Number(id))) throw new Error('Invalid product id');

        return {
          url: '/product',
          method: 'GET',
          params: { product_id: id },
        };
      },
    }),
    deleteProduct: build.mutation({
      query: (id) => ({
        url: `/board/${id}`,
        method: 'DELETE',
        param: { product_id: id },
      }),
    }),
    addProduct: build.mutation({
      // async queryFn(arg, queryApi, extraOptions, baseQuery) {
      //   try {
      //     // space will be encoded to + for some reason in rtk query
      //     const encodedProductName = encodeURIComponent(arg);
      //     const response = await fetch(`${serverHost}/addProduct?product_name=${encodedProductName}`, {
      //       method: 'POST',
      //     });

      //     if (!response.ok) {
      //       return { error: { status: response.status, message: response.statusText } };
      //     }

      //     const data = await response.json();
      //     return { data };
      //   } catch (error) {
      //     return { error: { status: 'FETCH_ERROR', message: error.message } };
      //   }
      // },
      query: (body) => ({
        url: '/addProduct',
        method: 'POST',
        body,
      }),
    }),
    removeVariant: build.mutation({
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        try {
          // manually encode the variant
          const encodedVar = encodeURIComponent(arg.variant);
          const response = await fetch(`${serverHost}/deleteVariant?product_id=${arg.product_id}&variant=${encodedVar}`, {
            method: 'POST',
            headers: {
              'Authorization': localStorage.getItem(localStorageKeys.accessToken) || '',
            }
          });

          if (!response.ok) {
            return { error: { status: response.status, message: response.statusText } };
          }

          const data = await response.json();
          return { data };
        } catch (error) {
          return { error: { status: 'FETCH_ERROR', message: error.message } };
        }
      },
    }),
    updateProduct: build.mutation({
      query: (body) => ({
        url: '/product',
        method: 'PUT',
        body,
      }),
    }),
    getCurProductComponentToggle: build.query({
      query: () => '/componentToggle',
    }),
    updateCurProductComponentToggle: build.mutation({
      query: (body) => ({
        url: '/componentToggle',
        method: 'PUT',
        body,
      }),
    }),
    exportDefinitions: build.query({
      query: (params) => ({
        url: '/exportDefinitions',
        method: 'GET',
        params,
      }),
    }),
    getAllFeatures: build.query({
      query: (params) => ({
        url: '/allFeatures',
        method: 'GET',
        params,
      }),
    }),
    // deprecated 2025/07/01 use update feature roi
    updateFeature: build.mutation({
      query: ({ body, params }) => ({
        url: '/feature',
        method: 'PUT',
        body,
        params,
      }),
    }),
    // deprecated 2025/06/27 use add feature roi
    addFeature: build.mutation({
      query: ({ body, params }) => ({
        url: '/addFeature',
        method: 'POST',
        body,
        params,
      }),
    }),
    deleteFeature: build.mutation({
      async queryFn(arg, queryApi, extraOptions, baseQuery) {
        try {
          // space will be encoded to + for some reason in rtk query
          const encodedVariantName = encodeURIComponent(arg.variant);
          // const response = await fetch(`${serverHost}/feature?product_id=${arg.product_id}&step=${arg.step}&feature_id=${arg.feature_id}&variant=${encodedVariantName}`, {
          //   method: 'DELETE',
          // });
          let url = `${serverHost}/features?product_id=${arg.product_id}&step=${arg.step}&feature_id=${arg.feature_id}`;
          if (_.isInteger(arg.component_id)) url += `&component_id=${arg.component_id}`;
          const response = await fetch(url, {
            method: 'DELETE',
            headers: {
              'Authorization': localStorage.getItem(localStorageKeys.accessToken) || '',
            }
          });

          if (!response.ok) {
            return { error: { status: response.status, message: response.statusText } };
          }

          const data = await response.json();
          return { data };
        } catch (error) {
          return { error: { status: 'FETCH_ERROR', message: error.message } };
        }
      },
    }),
    measureProductDimension: build.mutation({
      query: (body) => ({
        url: '/measure',
        method: 'POST',
        body,
      }),
    }),
    cadParse: build.mutation({
      query: (body) => ({
        url: '/previewCadFeaturePoints',
        method: 'POST',
        body,
      }),
    }),
    getAllCategories: build.query({
      query: () => '/component_template/categories'
    }),
    getComponentTemplate: build.query({
      query: (params) => ({
        url: '/component_template/search',
        method: 'GET',
        params,
      }),
    }),
    getComponentTemplateDetailById: build.query({
      query: (id) => {
        if (_.isNull(id) || !_.isInteger(Number(id))) throw new Error('id is invalid');

        return {
          url: '/component_template',
          method: 'GET',
          params: { id },
        };
      },
    }),
    addComponentTemplate: build.mutation({
      query: (body) => ({
        url: '/component_template/add',
        method: 'POST',
        body,
      }),
    }),
    updateComponentTemplate: build.mutation({
      query: (body) => ({
        url: '/component_template/update',
        method: 'PUT',
        body,
      }),
    }),
    componentTemplateMap: build.mutation({
      query: (body) => ({
        url: '/component_template/map',
        method: 'PUT',
        body,
      }),
    }),
    addComponent: build.mutation({
      query: (body) => ({
        url: '/component/add',
        method: 'POST',
        body: {
          ...body,
          part_no: _.isEmpty(body.part_no) ? null : body.part_no,
          package_no: _.isEmpty(body.package_no) ? null : body.package_no,
        },
      }),
    }),
    getProductComponent: build.query({
      query: (params) => ({
        url: '/component',
        method: 'GET',
        params,
      }),
    }),
    registerProductFeature: build.mutation({
      query: (body) => ({
        url: '/registerFeatures',
        method: 'POST',
        body,
      }),
    }),
    updateComponent: build.mutation({
      query: ({ body, params }) => ({
        url: '/component/update',
        method: 'PUT',
        body: {
          ...body,
          part_no: _.isEmpty(body.part_no) ? null : body.part_no,
          package_no: _.isEmpty(body.package_no) ? null : body.package_no,
        },
        params,
      }),
    }),
    deleteComponent: build.mutation({
      query: (params) => ({
        url: '/component',
        method: 'DELETE',
        params,
      }),
    }),
    cloneGoldenProduct: build.mutation({
      query: (body) => ({
        url: '/cloneGoldenProduct',
        method: 'POST',
        body
      }),
    }),
    detectMarkerCenter: build.mutation({
      query: (body) => ({
        url: '/detectMarkerCenter',
        method: 'PUT',
        body,
      }),
    }),
    shouldUpdateModels: build.mutation({
      query: (body) => ({
        url: '/shouldUpdateModels',
        method: 'PUT',
        body,
      }),
    }),
    scanBarcode: build.mutation({
      query: (params) => ({
        url: '/barcode',
        method: 'POST',
        params,
      }),
    }),
    getFeatureByFeatureId: build.query({
      query: (params) => ({
        url: '/feature',
        method: 'GET',
        params,
      }),
    }),
    getAgentParams: build.query({
      query: (params) => ({
        url: '/feature/parameters',
        method: 'GET',
        params,
      }),
    }),
    applyGroupLinkAction: build.mutation({
      query: (body) => ({
        url: '/component/setGroupByPartNo',
        method: 'POST',
        body,
      }),
    }),
    updateAgentParams: build.mutation({
      query: (body) => ({
        url: '/feature/parameters',
        method: 'PUT',
        body,
      }),
    }),
    applyPartNoLinkAction: build.mutation({
      query: (body) => ({
        url: '/component/setPartNoGroupByPackageNo',
        method: 'POST',
        body,
      }),
    }),
    getSampleComponent: build.query({
      query: (params) => ({
        url: '/component/example',
        method: 'GET',
        params,
      }),
    }),
    addFeatureRoi: build.mutation({
      query: ({ body, params }) => ({
        url: '/feature/rois',
        method: 'POST',
        body,
        params,
      }),
    }),
    updateFeatureRoi: build.mutation({
      query: ({ body, params }) => {
        if (body.feature_type  !== 'marker') body.line_item_params = null

        return {
          url: '/feature/rois',
          method: 'PUT',
          body,
          params,
        };
      },
    }),
    goldenRegisterArray: build.mutation({
      query: (body) => ({
        url: '/golden/registerArray',
        method: 'PUT',
        body,
      }),
    }),
    getArrayRegisteration: build.query({
      query: (params) => ({
        url: '/golden/array',
        method: 'GET',
        params,
      }),
    }),
    getAutoProgramConfig: build.query({
      query: () => '/autoProgramConfig',
    }),
    runFullAutoProgram: build.mutation({
      query: (body) => ({
        url: '/fullAutoProgram',
        method: 'POST',
        body,
      }),
    }),
    runSemiAutoProgram: build.mutation({
      query: (body) => ({
        url: '/semiAutoProgram',
        method: 'POST',
        body,
      }),
    }),
    deleteComponentTemplate: build.mutation({
      query: (id) => ({
        url: '/component_template/delete',
        method: 'DELETE',
        params: { id },
      }),
    }),
  }),
});

export const {
  useGetAllProductsQuery,
  useGetProductByIdQuery,
  useProductInspectablesRegisterMutation,
  useAddProductMutation,
  useUpdateProductMutation,
  useGetAllFeaturesQuery,
  useUpdateFeatureMutation,
  useAddFeatureMutation,
  useMeasureProductDimensionMutation,
  useDeleteFeatureMutation,
  useGetAllCategoriesQuery,
  useGetComponentTemplateQuery,
  useLazyGetComponentTemplateQuery,
  useGetComponentTemplateDetailByIdQuery,
  useAddComponentTemplateMutation,
  useUpdateComponentTemplateMutation,
  useComponentTemplateMapMutation,
  useGetProductComponentQuery,
  useCadParseMutation,
  useRegisterProductFeatureMutation,
  useUpdateComponentMutation,
  useDeleteComponentMutation,
  useCloneGoldenProductMutation,
  useDetectMarkerCenterMutation,
  useShouldUpdateModelsMutation,
  useDeleteProductMutation,
  useScanBarcodeMutation,
  useLazyGetFeatureByFeatureIdQuery,
  useLazyGetAllFeaturesQuery,
  useLazyGetProductComponentQuery,
  useLazyGetAgentParamsQuery,
  useApplyGroupLinkActionMutation,
  useUpdateAgentParamsMutation,
  useApplyPartNoLinkActionMutation,
  useLazyGetSampleComponentQuery,
  useAddComponentMutation,
  useAddFeatureRoiMutation,
  useUpdateFeatureRoiMutation,
  useGetAutoProgramConfigQuery,
  useRunFullAutoProgramMutation,
  useRunSemiAutoProgramMutation,
  useGetArrayRegisterationQuery,
  useGoldenRegisterArrayMutation,
  useDeleteComponentTemplateMutation,
  useLazyExportDefinitionsQuery,
} = productApi;