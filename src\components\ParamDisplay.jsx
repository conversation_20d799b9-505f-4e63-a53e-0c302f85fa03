import React from 'react';
import _ from 'lodash';

const ParamDisplay = (props) => {
  const {
    min,
    max,
    inferenceResult,
    threshold,
    minThreshold,
    maxThreshold,
    type = 'range', // 'range' for min/max thresholds, 'single' for single threshold
    isGreenFromMinToThreshold = true // For single type: true = min to threshold is green, false = threshold to max is green
  } = props;

  // Determine if we're using single threshold or range thresholds
  const useSingleThreshold = type === 'single' && threshold !== undefined;
  const useRangeThresholds = type === 'range' && minThreshold !== undefined && maxThreshold !== undefined;

  // Calculate position percentages
  const totalRange = max - min;
  let inferencePosition = ((inferenceResult - min) / totalRange) * 100;

  // Clamp the position to the display range (0-100%)
  const clampedInferencePosition = Math.max(0, Math.min(100, inferencePosition));

  let okMin, okMax;
  if (useSingleThreshold) {
    // For single threshold, determine green range based on isGreenFromMinToThreshold
    if (isGreenFromMinToThreshold) {
      okMin = min;
      okMax = threshold;
    } else {
      okMin = threshold;
      okMax = max;
    }
  } else if (useRangeThresholds) {
    okMin = minThreshold;
    okMax = maxThreshold;
  } else {
    // Default case, no valid thresholds
    okMin = min;
    okMax = max;
  }

  const okMinPosition = ((okMin - min) / totalRange) * 100;
  const okMaxPosition = ((okMax - min) / totalRange) * 100;
  const okWidth = okMaxPosition - okMinPosition;

  // Determine if inference result is within the OK range
  const isInOkRange = useSingleThreshold
    ? (isGreenFromMinToThreshold
        ? inferenceResult <= threshold
        : inferenceResult >= threshold)
    : (useRangeThresholds
        ? (inferenceResult >= okMin && inferenceResult <= okMax)
        : true);

  // Determine text color based on whether value is in OK range
  const textColor = isInOkRange ? 'text-green-600' : 'text-red';

  // Pre-calculate rounded value to avoid multiple calculations
  const roundedValue = _.round(inferenceResult, 2);

  return (
    <div className="self-stretch inline-flex justify-start items-start gap-4 w-full">
      <div className="flex-1 rounded-tl-sm rounded-tr-sm inline-flex flex-col justify-start items-start">
        {/* Inference value indicator */}
        <div className="self-stretch h-[20px] relative">
          <div className='absolute top-[-0px]'
            style={{
              left: `${clampedInferencePosition}%`,
              transform: 'translateX(-50%)'
            }}
          >
            <span
              className={`font-source text-[12px] font-normal leading-[150%] ${textColor} whitespace-nowrap`}
              title={roundedValue}
            >
              {roundedValue}
            </span>
          </div>
          <img
            src="/icn/yellow_triangle.svg"
            alt="warning"
            className="absolute w-[16px] h-[16px] top-[-4px]"
            style={{ left: `calc(${clampedInferencePosition}% - 8px)` }}
          />
        </div>

        {/* Range bar */}
        <div className="self-stretch h-6 relative bg-rose-500 rounded-sm outline outline-1 outline-offset-[-1px] outline-neutral-200 inline-flex justify-start items-start gap-2.5 overflow-hidden">
          {/* OK range (green area) */}

            <div className={`absolute h-6 self-stretch [background:var(--Passed,#42BE65)]`}
              style={{
                left: `${okMinPosition}%`,
                width: `${okWidth}%`,
              }}
            />

          {/* Current inference value line */}
          <div className="w-[3px] h-6 absolute bg-stone-300"
            style={{
              left: `calc(${clampedInferencePosition}% - 1.5px)`,
            }}
          />

          {/* Threshold labels */}
          {useRangeThresholds ? (
            <>
              {/* Right threshold label - positioned to the right */}
              <div className="top-[2.50px] absolute text-left text-zinc-800 text-sm font-semibold font-['Source_Sans_Pro'] tracking-wide"
                style={{ left: `calc(${okMaxPosition}% + 5px)` }}
              >
                {_.round(okMax, 2)}
              </div>
              {/* Left threshold label - positioned to the left */}
              <div className="top-[2.50px] absolute text-right text-zinc-800 text-sm font-semibold font-['Source_Sans_Pro'] tracking-wide"
                style={{ right: `calc(${100 - okMinPosition}% + 5px)` }}
              >
                {_.round(okMin, 2)}
              </div>
            </>
          ) : useSingleThreshold ? (
            <div className="top-[2.50px] absolute text-center justify-center text-zinc-800 text-sm font-semibold font-['Source_Sans_Pro'] tracking-wide"
              style={{ left: `calc(${okMaxPosition}%)` }}
            >
              {_.round(okMax, 2)}
            </div>
          ) : null}
        </div>

        {/* Min and Max labels */}
        <div className="self-stretch py-0.5 relative inline-flex justify-between items-start">
          <div className="w-6 justify-center text-neutral-200 text-xs font-normal font-['Source_Sans_Pro'] leading-none tracking-tight">
            {_.round(min, 2)}
          </div>
          <div className="text-center justify-center text-neutral-200 text-xs font-normal font-['Source_Sans_Pro'] leading-none tracking-tight">
            {_.round(max, 2)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParamDisplay;
