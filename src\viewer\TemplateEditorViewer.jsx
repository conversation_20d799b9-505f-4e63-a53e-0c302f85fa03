import React, { Fragment, useCallback, useEffect, useRef, useState } from 'react';
import { generalDrawRectMouseDownHandle, generalDrawRectMouseMoveHandle, generalPanZoomMouseDownHandler, generalPanZoomMouseMoveHandler, generalPanZoomMouseUpHandler, generalPanZoomMouseWheelHandler, generateAllAgentParamsDisplay, generateExtendedRoiByFeature, generateFeatureDirection, generateLeadSegmentation, generateSelectedAgentParamDisplayObj, getComponentCenterByRoiDtoObj, getComponentRectInfoByFeatures, getComponentRoiPminPaxByFeature, getPositionRotatedAroundPoint, getRectInnerCenter, getRotatedRectBoundingBox, getTranslationInRotatedSystem, getTwoDRectPminPmax, handleSceneCopyComponentRect, initLeadDefaultLintItemParams, initMountingDefaultLineItemParams, initSolderDefaultLineItemParams, initTextVerificationDefaultLineItemParams, isMounting2DPolarityRoiEnabled, loadHighResolScene, loadInitFullSizeThumbnail, middlePanZoomMouseDownHandler, handleSceneRemoveComponentWithRelatedFeatures, rotatePoint, updateAllFeaturesByUpdatedExtendedRoi, zoomPanToObject, handleSceneCopyFeatureIntoComponent, handleSceneRemoveFeatureFromComponent, handleSceneRemoveFeature, handleSceneCopyBatchUngroupedFeatures, isSolderFeatureAndProfileRoiEnabled, initBarcodeScannerDefaultLineItemParams, handleSceneRemoveComponentOnly, updatePolarityNMaskROIByFeatureScalingEvent, removeProfileHeightFromPayload } from './util';
import { agentParamUserActionTypes, backgroundRoi1, backgroundRoi2, componentRoiPadding, defaultNewFeatureExtRoiPadding, directionArrow, displayableAgentParamsWhenComponentSelected, extBottom, extendedRoi, extendedRoiRelatedLineItems, extTop, highResoluRefreshInterval, isAOI2DSMT, isAOI3DSMT, keyboardSubmitInterval, leadInspection3D, leadSegmentationRects, maskRoi, minFeatureBoxLength, mountingFeatureType, mountingInspection2D, mountingInspection3D, newRectStrokeWidth, polarityRoi, profileHeight, profileRoi, profileWidth, solderInspection3D, updateFeatureInGroup } from '../common/const';
import { useAddComponentMutation, useAddFeatureMutation, useAddFeatureRoiMutation, useComponentTemplateMapMutation, useDeleteComponentMutation, useDeleteFeatureMutation, useLazyGetAgentParamsQuery, useLazyGetAllFeaturesQuery, useUpdateAgentParamsMutation, useUpdateComponentMutation, useUpdateFeatureMutation, useUpdateFeatureRoiMutation, useLazyGetComponentTemplateQuery, useAddComponentTemplateMutation, useUpdateComponentTemplateMutation } from '../services/product';
import { Button } from 'antd';
import _ from 'lodash';
import { useGetImageMetaDataQuery } from '../services/camera';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled, setTransparentLoadingEnabled } from '../reducer/setting';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { applyTemplateString, buildSearchTerms, getColorByStr, sleep } from '../common/util';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { setAgentParamUserAction } from '../reducer/productDefine';
import { systemApi } from '../services/system';
import AddComponentBySelectedFeatures from '../modal/AddComponentBySelectedFeatures';
import AddComponentTemplate from '../modal/AddComponentTemplate';


const TemplateEditorViewer = (props) => {
	const {
		curProduct,
		selectedTool,
		allFeatures,
		selectedFid,
		selectedCid,
		selectedLineItemName,
		setSelectedFid,
		fcanvasRef,
		allComponents,
		setSelectedCid,
		setSelectedLineItemName,
		refetchAllFeatures,
		refetchAllComponents,
		requiredLocateRect,
		selectedUngroupedFid,
		setSelectedUngroupedFid,
		setHoveredCid,
		setHoveredFid,
		setHoveredLineItemName,
		selectedAgentParam,
		setSelectedAgentParam,
		handleSubmitSelect3DArea,
    updateAllFeaturesState,
    allFeaturesRef,
    allComponentsRef,
    mmToPixelRatio,
    refetchAllFeatureReevaluationResult,
    selectedArrayIndex,
    setSelectedArrayIndex,
    setSelectedFeatureType,
    setSelectedPartNo,
    setSelectedPackageNo,
    setSelectedScope,
    componentDetailWidth,
	} = props;

  const dispatch = useDispatch();
  const { t } = useTranslation();

  const canvasElRef = useRef();
  // const fcanvasRef = useRef();
  const isPanning = useRef(false);
  const displayedHighResolSceneRef = useRef();
  const thumbnailBgSceneRef = useRef();
  const displayedFeaturesRef = useRef([]);
  const displayedComponentBoxRef = useRef([]);
  const viewerContainerRef = useRef();
  const selectedCidRef = useRef(null);
  const selectedFidRef = useRef(null);
  const selectedArrayIndexRef = useRef(null);
  const selectedLineItemNameRef = useRef(null);
  const selectedUngroupedFidRef = useRef(null);
  const defaultPanZoomMouseDownEvent = useRef();
  const defaultPanZoomMouseUpEvent = useRef();
  const defaultPanZoomMouseMoveEvent = useRef();
  const defaultPanZoomMouseWheelEvent = useRef();
  const containerRef = useRef(null);
  const displayedAgentParamObjsRef = useRef([]);
  const curDrawingRectRef = useRef(null);
  const drawingInitMousePosRef = useRef(null);
  const curSelectedRectsRef = useRef([]);
  const selectedAgentParamRef = useRef(null);
  const transformModeDirectionKeydownEventRef = useRef(null);
  const rectDirectionKeydownEventRef = useRef(null);
  const globalKeydownEventRef = useRef(null);
  const isMouseInCanvasRef = useRef(false);

  const [curDrawBoxDim, setCurDrawBoxDim] = useState(null);
  const [selectionOptionPos, setSelectionOptionPos] = useState(null);
  const [selectDimensionMousePos, setSelectDimensionMousePos] = useState({});
  const [isAddComponentModalOpened, setIsAddComponentModalOpened] = useState(false);
  const [privateTemplateId, setPrivateTemplateId] = useState(null);
  const [isAddTemplateModalOpened, setIsAddTemplateModalOpened] = useState(false);
  const [templatePackageNo, setTemplatePackageNo] = useState('');
  const [templatePartNo, setTemplatePartNo] = useState('');
  const [componentToSave, setComponentToSave] = useState(null);

  const [updateFeature] = useUpdateFeatureMutation();
  const [updateFeatureRoi] = useUpdateFeatureRoiMutation();
  const [updateGroupAgentParams] = useUpdateAgentParamsMutation();
  const [updateComponent] = useUpdateComponentMutation();
  const [deleteFeature] = useDeleteFeatureMutation();
  const { data: curImageMetadata } = useGetImageMetaDataQuery({ uri: _.get(curProduct, 'inspectables[0].color_map_uri') });
  const [addFeature] = useAddFeatureMutation();
  const [addFeatureRoi] = useAddFeatureRoiMutation();
  const [addComponent] = useAddComponentMutation();
  const [physicalCoordMap] = useComponentTemplateMapMutation();
  const [deleteComponent] = useDeleteComponentMutation();
  const [lazyGetAgentParams] = useLazyGetAgentParamsQuery();
  const [lazyGetAllFeatures] = useLazyGetAllFeaturesQuery();
  const [lazyGetPrivateTemplate] = useLazyGetComponentTemplateQuery();
  const [addComponentTemplate] = useAddComponentTemplateMutation();
  const [updateComponentTemplate] = useUpdateComponentTemplateMutation();

  const { data: systemMetadata } = useSelector((state) => systemApi.endpoints.getSystemMetadata.select()(state));

  const handleAddComponentTemplate = async (packageNo, partNo, selectedArrayIndex) => {
    if (!componentToSave) return;
    const relatedFeatures = _.filter(allFeatures, f => f.group_id === componentToSave.region_group_id && f.array_index === selectedArrayIndex);
    const canned_rois = _.map(relatedFeatures, f => ({
      shape: {
        type: 'obb',
        points: [
          { x: _.round(_.get(f, 'roi.points[0].x', 0) / mmToPixelRatio, 2), y: _.round(_.get(f, 'roi.points[0].y', 0) / mmToPixelRatio, 2) },
          { x: _.round(_.get(f, 'roi.points[1].x', 0) / mmToPixelRatio, 2), y: _.round(_.get(f, 'roi.points[1].y', 0) / mmToPixelRatio, 2) },
        ],
        angle: _.get(f, 'roi.angle', 0),
      },
      checklist: _.get(f, 'line_item_params', {}),
      // feature_type: _.get(f, 'feature_type', ''),
    }));
    const centerPixel = getComponentCenterByRoiDtoObj(_.get(componentToSave, 'shape', {}));
    const additionalSearchTerms = buildSearchTerms([
      partNo,
      packageNo,
    ]);
    const payload = {
      builtin: false,
      memo: '',
      category: '',
      package_no: packageNo,
      part_no: partNo,
      additional_search_terms: additionalSearchTerms,
      model: {
        center: { x: _.round(centerPixel.x / mmToPixelRatio, 2), y: _.round(centerPixel.y / mmToPixelRatio, 2) },
        canned_rois,
        image_uri: _.get(componentToSave, 'color_map_uri', ''),
      },
    };

    const res = await addComponentTemplate(payload);
    if (res.error) {
      aoiAlert(t('notification.error.saveComponentTemplate'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
    } else {
      aoiAlert(t('notification.success.saveComponentTemplate'), ALERT_TYPES.COMMON_SUCCESS);
    }
    setIsAddTemplateModalOpened(false);
    setComponentToSave(null);
    setSelectionOptionPos(null);
  };

  const loadFeaturesToScene = (
    features,
    selectedFid,
    selectedCid,
    selectedLineItemName,
    components,
    selectedAgentParam,
    allFeatures,
    selectedUngroupedFid,
    productObj,
    updateAllFeaturesState,
    selectedArrayIndex,
  ) => {
    const featuresInComponent = _.filter(allFeatures, f => f.group_id === selectedCid);

    for (const feature of features) {
      const component = _.find(components, c => c.region_group_id === feature.group_id);

      // get the component's center as feature's rotation center(reference)
      const componentCenter = {
        x: _.get(component, 'shape.points[0].x', 0) + (_.get(component, 'shape.points[1].x', 0) - _.get(component, 'shape.points[0].x', 0) + 1) / 2,
        y: _.get(component, 'shape.points[0].y', 0) + (_.get(component, 'shape.points[1].y', 0) - _.get(component, 'shape.points[0].y', 0) + 1) / 2,
      };

      const curRect = new fabric.Rect({
        left: _.get(feature, 'roi.points[0].x', 0) - newRectStrokeWidth,
        top: _.get(feature, 'roi.points[0].y', 0) - newRectStrokeWidth,
        width: _.get(feature, 'roi.points[1].x', 0) - _.get(feature, 'roi.points[0].x', 0) + newRectStrokeWidth + 1,
        height: _.get(feature, 'roi.points[1].y', 0) - _.get(feature, 'roi.points[0].y', 0) + newRectStrokeWidth + 1,
        fill: 'transparent',
        stroke: 'white',
        strokeWidth: newRectStrokeWidth,
        strokeUniform: true,
        evented: false,
        selectable: true,
      });

      curRect.set('originalRectTopLeftWithZeroRotation', {
        left: _.get(feature, 'roi.points[0].x', 0),
        top: _.get(feature, 'roi.points[0].y', 0),
      });

      curRect.set('originalRectInnerDim', {
        width: _.get(feature, 'roi.points[1].x', 0) - _.get(feature, 'roi.points[0].x', 0) + 1,
        height: _.get(feature, 'roi.points[1].y', 0) - _.get(feature, 'roi.points[0].y', 0) + 1,
      });

      curRect.set('fid', _.get(feature, 'feature_id'));
      curRect.set('gid', _.get(feature, 'group_id'));
      curRect.set('featureObj', feature);

      if (_.get(feature, 'roi.angle', 0) !== 0) {
        // apply rotation
        curRect.rotate(_.get(feature, 'roi.angle', 0));
        curRect.set('originalAng', _.get(feature, 'roi.angle', 0));
      }

      curRect.set('originalRotatedCenter', curRect.getCenterPoint());

      if (selectedTool === 'select') {
        if (!_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
          if (_.isInteger(feature.group_id)) {
            curRect.set('selectable', false);
            curRect.set('evented', false);
          } else {
            curRect.set('selectable', true);
            curRect.set('evented', true);
            if (selectedUngroupedFid === feature.feature_id) {
              curRect.set('stroke', '#FEB617');

              curRect.onDeselect = (opt) => {
                if (!fcanvasRef.current || !curRect) return;
                if (_.isNull(selectedUngroupedFid) || _.isUndefined(opt.e)) return;
                // check if the mouse is outside of the current object
                const point = fcanvasRef.current.getPointer(opt.e, true);
                if (!curRect.containsPoint(point)) {
                  // only reset if deselect outside of the current component
                  if (_.isEmpty(opt.object) || _.isEmpty(opt.object.get('agentParamLabel'))) {
                    setSelectedUngroupedFid(null);
                  }
                  setSelectedFid(null);
                  setSelectedLineItemName(null);
                  setHoveredFid(null);
                  setHoveredLineItemName(null);
                  curSelectedRectsRef.current = [];
                }
              };

              curRect.on('modified', () => {
                dispatch(setIsContainerLvlLoadingEnabled(true));
                dispatch(setContainerLvlLoadingMsg(t('loader.updatingFeature')));

                const submit = async (feature, curRect) => {
                  let newFeature;

                  const curFeatureCenter = curRect.getCenterPoint();

                  if (curRect.get('scaleX') !== 1 || curRect.get('scaleY') !== 1) {
                    // scaling
                    const newFeatureRectInnerDimension = {
                      width: (curRect.get('originalRectInnerDim').width + newRectStrokeWidth) * curRect.get('scaleX') - newRectStrokeWidth,
                      height: (curRect.get('originalRectInnerDim').height + newRectStrokeWidth) * curRect.get('scaleY') - newRectStrokeWidth,
                    };

                    newFeature = {
                      ...feature,
                      roi: {
                        ...feature.roi,
                        points: [
                          {
                            x: _.round(curFeatureCenter.x - newFeatureRectInnerDimension.width / 2, 0),
                            y: _.round(curFeatureCenter.y - newFeatureRectInnerDimension.height / 2, 0),
                          },
                          {
                            x: _.round(curFeatureCenter.x + newFeatureRectInnerDimension.width / 2 - 1, 0),
                            y: _.round(curFeatureCenter.y + newFeatureRectInnerDimension.height / 2 - 1, 0),
                          }
                        ]
                      }
                    };

                    if (curRect.get('scaleX') < 1 || curRect.get('scaleY') < 1) {
                      // also update the extended roi if it exists
                      newFeature = updatePolarityNMaskROIByFeatureScalingEvent(newFeature);
                    }

                    if (isSolderFeatureAndProfileRoiEnabled(newFeature) && isAOI3DSMT) {
                      newFeature = {
                        ...newFeature,
                        line_item_params: {
                          [solderInspection3D]: {
                            ..._.get(newFeature, `line_item_params.${solderInspection3D}`, {}),
                            params: {
                              ..._.get(newFeature, `line_item_params.${solderInspection3D}.params`, {}),
                              [profileHeight]: {
                                ..._.get(newFeature, `line_item_params.${solderInspection3D}.params.${profileHeight}`, {}),
                                param_int: {
                                  ..._.get(newFeature, `line_item_params.${solderInspection3D}.params.${profileHeight}.param_int`, {}),
                                  value: _.floor(_.get(newFeature, `roi.points[1].y`, 0) - _.get(newFeature, `roi.points[0].y`, 0) + 1 +
                                  _.get(newFeature, `line_item_params.${solderInspection3D}.params.${extTop}.param_int.value`, 0) +
                                  _.get(newFeature, `line_item_params.${solderInspection3D}.params.${extBottom}.param_int.value`, 0), 0),
                                },
                              },
                            }
                          },
                        },
                      };

                      if (curRect.get('scaleX') < 1) {
                        // also update the profile width to min(feature roi width, profile width)
                        newFeature = {
                          ...newFeature,
                          line_item_params: {
                            [solderInspection3D]: {
                              ..._.get(newFeature, `line_item_params.${solderInspection3D}`, {}),
                              params: {
                                ..._.get(newFeature, `line_item_params.${solderInspection3D}.params`, {}),
                                [profileWidth]: {
                                  ..._.get(newFeature, `line_item_params.${solderInspection3D}.params.${profileWidth}`, {}),
                                  param_int: {
                                    ..._.get(newFeature, `line_item_params.${solderInspection3D}.params.${profileWidth}.param_int`, {}),
                                    value: _.min([
                                      _.get(newFeature, `roi.points[1].x`, 0) - _.get(newFeature, `roi.points[0].x`, 0) + 1,
                                      _.get(newFeature, `line_item_params.${solderInspection3D}.params.${profileWidth}.param_int.value`, 0),
                                    ])
                                  },
                                },
                              }
                            },
                          },
                        };
                      }
                    }
                  } else if (curRect.get('angle') !== _.get(feature, 'roi.angle', 0)) {
                    // rotation
                    newFeature = {
                      ...feature,
                      roi: {
                        ...feature.roi,
                        angle: curRect.get('angle'),
                      }
                    };
                  } else {
                    // translation
                    newFeature = {
                      ...feature,
                      roi: {
                        ...feature.roi,
                        points: [
                          {
                            x: _.round(curFeatureCenter.x - curRect.get('originalRectInnerDim').width / 2, 0),
                            y: _.round(curFeatureCenter.y - curRect.get('originalRectInnerDim').height / 2, 0),
                          },
                          {
                            x: _.round(curFeatureCenter.x + curRect.get('originalRectInnerDim').width / 2 - 1, 0),
                            y: _.round(curFeatureCenter.y + curRect.get('originalRectInnerDim').height / 2 - 1, 0),
                          }
                        ]
                      }
                    };
                  }

                  const res = await updateFeatureRoi({ body: newFeature, params: { allComponents: _.isInteger(newFeature.group_id) } });

                  if (res.error) {
                    aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
                    console.error(res.error.message);
                    dispatch(setIsContainerLvlLoadingEnabled(false));
                    dispatch(setContainerLvlLoadingMsg(''));
                  }

                  // await updateAllFeaturesState([feature.feature_id], 'update', [newFeature]);
                  if (!_.isEmpty(res.data)) {
                    await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', _.map(res.data, f => {
                      return {
                        ...f,
                        line_item_params: feature.line_item_params,
                      };
                    }));
                  }
                  dispatch(setIsContainerLvlLoadingEnabled(false));
                  dispatch(setContainerLvlLoadingMsg(''));
                };

                submit(feature, curRect);
              });

              fcanvasRef.current.setActiveObject(curRect);
            } else {
              curRect.set('hoverCursor', 'pointer');
              curRect.on('mousedown', (opt) => {
                if (opt.e.button === 0) {
                  setSelectedUngroupedFid(curRect.get('fid'));
                  setSelectedFid(null);
                  setHoveredFid(null);
                  setHoveredCid(null);
                }
              });
              curRect.on('mouseover', () => {
                curRect.set('stroke', '#FEB617');
                fcanvasRef.current.renderAll();
              });
              curRect.on('mouseout', () => {
                curRect.set('stroke', 'white');
                fcanvasRef.current.renderAll();
              });
            }
          }
        } else if (_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
          curRect.set('stroke', '#56CCF2');
          curRect.set('selectable', false);
          curRect.set('evented', true);
          curRect.set('hoverCursor', 'pointer');
          curRect.on('mouseover', () => {
            curRect.set('stroke', '#FEB617');
            fcanvasRef.current.renderAll();
            setHoveredFid(curRect.get('fid'));
          });
          curRect.on('mouseout', () => {
            curRect.set('stroke', '#56CCF2');
            fcanvasRef.current.renderAll();
            setHoveredFid(null);
          });
          curRect.on('mousedown', (opt) => {
            if (opt.e.button === 0) {
              setSelectedFid(curRect.get('fid'));
              setSelectedLineItemName(null);
              setHoveredFid(null);
              setHoveredCid(null);
              setSelectedUngroupedFid(null);
              setSelectedFeatureType(curRect.get('featureObj').feature_type);
            }
          });

          let allAgentDisplayObjs = generateAllAgentParamsDisplay(
            feature,
            component,
            featuresInComponent,
            mmToPixelRatio,
            t,
            false,
            productObj,
            setSelectedAgentParam,
            fcanvasRef,
          );

          allAgentDisplayObjs = _.filter(
            allAgentDisplayObjs,
            obj => _.includes(displayableAgentParamsWhenComponentSelected, obj.get('agentParamLabel'))
          );

          if (!_.isEmpty(allAgentDisplayObjs)) {
            for (const obj of allAgentDisplayObjs) {
              fcanvasRef.current.add(obj);
              displayedAgentParamObjsRef.current.push(obj);
            }
          }
        } else if (_.isInteger(selectedCid) && _.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
          if (!_.includes([0, null], _.get(feature, 'array_index', null))) {
            // disable translation, rotation, and scaling
            curRect.set('evented', true);
            curRect.set('selectable', true);
            curRect.set('stroke', '#56CCF2');

            curRect.setControlsVisibility({
              mt: false,
              mb: false,
              ml: false,
              mr: false,
              bl: false,
              br: false,
              tl: false,
              tr: false,
              mtr: false,
            });

            curRect.lockMovementX = true;
            curRect.lockMovementY = true;

            curRect.hoverCursor = 'default';
          } else {
            curRect.set('evented', true);
            curRect.set('selectable', true);
            curRect.set('stroke', '#56CCF2');

            curRect.on('mouseup', (opt) => {
              if (opt.e.button === 2) {
                opt.e.preventDefault();
                // right click show options for the selected region
                setSelectionOptionPos({
                  x: opt.e.clientX - containerRef.current.getBoundingClientRect().left,
                  y: opt.e.clientY - containerRef.current.getBoundingClientRect().top,
                  type: 'feature',
                });
              }
            });

            curRect.on('modified', () => {
              // update feature
              dispatch(setIsContainerLvlLoadingEnabled(true));
              dispatch(setContainerLvlLoadingMsg(t('loader.updatingFeature')));

              handleFeatureRectUpdate({
                allComponents: components,
                curRect,
                allFeatures,
                selectedFid,
                selectedCid,
                selectedArrayIndex,
              });
            });
          }

          curRect.onDeselect = (opt) => {
            if (!fcanvasRef.current || !curRect) return;
            if (_.isNull(selectedFid) || _.isUndefined(opt.e)) return;
            // check if the mouse is outside of the current object
            const point = fcanvasRef.current.getPointer(opt.e, true);
            if (!curRect.containsPoint(point)) {
              // only reset if deselect outside of the current component
              // now the agent related roi is also selectable so consider this as well
              if (_.isEmpty(opt.object) || _.isEmpty(opt.object.get('agentParamLabel'))) setSelectedFid(null);
              setSelectedUngroupedFid(null);
              setSelectedLineItemName(null);
              setHoveredFid(null);
              setHoveredLineItemName(null);
              curSelectedRectsRef.current = [];
              setSelectedFeatureType(null);
            }
          };

          if (_.isEmpty(selectedAgentParam)) {
            // load all displayable agent params (ex. extended roi, 2d polarity roi, etc.) but do not make them interactive
            const agentParamObjs = generateAllAgentParamsDisplay(
              feature,
              component,
              featuresInComponent,
              mmToPixelRatio,
              t,
              true,
              productObj,
              setSelectedAgentParam,
              fcanvasRef,
            );

            if (!_.isEmpty(agentParamObjs)) {
              for (const obj of agentParamObjs) {
                fcanvasRef.current.add(obj);
                displayedAgentParamObjsRef.current.push(obj);
              }
            }
            fcanvasRef.current.setActiveObject(curRect);
          } else {
            // load the selected agent param interactive obj
            const agentParamObjs = generateSelectedAgentParamDisplayObj(
              feature,
              component,
              selectedAgentParam,
              t,
              updateFeature,
              refetchAllFeatures,
              setSelectedAgentParam,
              _.filter(allFeatures, f => f.group_id === selectedCid),
              updateComponent,
              refetchAllComponents,
              fcanvasRef,
              updateAllFeaturesState,
              mmToPixelRatio,
              updateGroupAgentParams,
            );

            if (agentParamObjs) {
              for (let i = 0; i < agentParamObjs.length; i += 1) {
                const agentParamObj = agentParamObjs[i];
                fcanvasRef.current.add(agentParamObj);
                displayedAgentParamObjsRef.current.push(agentParamObj);
                if (i === 0) fcanvasRef.current.setActiveObject(agentParamObj);
              }
            }

            curRect.set('selectable', false);
            curRect.set('evented', false);
          }
        }
      }

      fcanvasRef.current.add(curRect);
      displayedFeaturesRef.current.push(curRect);
    }

    updateZIndex(selectedCid, selectedFid, selectedLineItemName, selectedUngroupedFid, selectedAgentParam);
  };

  const loadComponentsToScene = (
    components,
    selectedCid,
    selectedFid,
    selectedLineItemName,
    selectedUngroupedFid,
    selectedAgentParam,
    curProduct,
    selectedArrayIndex,
  ) => {
    for (const c of components) {
      const pMin = _.get(c, 'shape.points[0]', 0);
      const pMax = _.get(c, 'shape.points[1]', 0);

      // this position is based on the original image size
      // hence we need to scale it to the current scene size
      let newLeft = 0;
      let newTop = 0;
      let newWidth = 0;
      let newHeight = 0;

      newLeft = pMin.x;
      newTop = pMin.y;
      newWidth = pMax.x - pMin.x;
      newHeight = pMax.y - pMin.y;

      // backend will include the pMax point so...
      newLeft -= newRectStrokeWidth;
      newTop -= newRectStrokeWidth;
      newWidth += newRectStrokeWidth + 1;
      newHeight += newRectStrokeWidth + 1;

      const curRect = new fabric.Rect({
        left: newLeft,
        top: newTop,
        width: newWidth,
        height: newHeight,
        fill: 'transparent',
        stroke: 'white',
        strokeWidth: newRectStrokeWidth,
        strokeUniform: true,
        selectable: false,
        evented: false,
      });

      curRect.set('gid', _.get(c, 'region_group_id', 0));

      curRect.set('originalRotatedCenter', {
        x: newLeft + (newWidth - curRect.strokeWidth) / 2  + curRect.strokeWidth,
        y: newTop + (newHeight - curRect.strokeWidth) / 2 + curRect.strokeWidth,
      });

      curRect.set('originalRectTopLeftWithZeroRotation', {
        left: newLeft,
        top: newTop,
      });

      curRect.set('originalRectInnerDim', {
        width: pMax.x - pMin.x + 1,
        height: pMax.y - pMin.y + 1,
      });

      if (_.get(c, 'shape.type', 'obb') === 'obb') {
        curRect.set('originalAng', _.get(c, 'shape.angle', 0));
        curRect.rotate(_.get(c, 'shape.angle', 0));
      }

      curRect.set('componentObj', c);

      if (selectedTool === 'select') {
        if (!_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
          curRect.set('evented', true);
          curRect.set('hoverCursor', 'pointer');

          curRect.on('mouseup', (opt) => {
            if (opt.e.button === 0) {
              setHoveredCid(null);
              setSelectedCid(curRect.get('gid'));
              setSelectedFid(null);
              setSelectedUngroupedFid(null);
              fcanvasRef.current.setActiveObject(curRect);
              if (_.isInteger(curRect.get('componentObj').array_index)) {
                setSelectedArrayIndex(curRect.get('componentObj').array_index);
              }

              setSelectedPartNo(curRect.get('componentObj').part_no);
              setSelectedPackageNo(curRect.get('componentObj').package_no);
            }
          });

          curRect.on('mouseover', () => {
            curRect.set('stroke', '#FEB617');
            fcanvasRef.current.renderAll();
            setHoveredCid(curRect.get('gid'));
          });
          curRect.on('mouseout', (opt) => {
            curRect.set('stroke', 'white');
            fcanvasRef.current.renderAll();
            setHoveredCid(null);
          });
        } else if (_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
          if ((_.isInteger(selectedArrayIndex) && curRect.get('componentObj').array_index === selectedArrayIndex) || !_.isInteger(selectedArrayIndex)) {
            fcanvasRef.current.setActiveObject(curRect);
          }

          if (_.includes([0, null], _.get(c, 'array_index', null))) {
            curRect.setControlsVisibility({
              mt: false,
              mb: false,
              ml: false,
              mr: false,
              tl: false,
              tr: false,
              bl: false,
              br: false,
            });
          } else {
            curRect.setControlsVisibility({
              mt: false,
              mb: false,
              ml: false,
              mr: false,
              tl: false,
              tr: false,
              bl: false,
              br: false,
              mtr: false,
            });

            curRect.lockMovementX = true;
            curRect.lockMovementY = true;
            curRect.hoverCursor = 'default';
          }

          curRect.on('mouseup', (opt) => {
            if (opt.e.button === 2) {
              opt.e.preventDefault();
              // right click show options for the selected region
              const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);
              if (componentObj && !_.isEmpty(componentObj.part_no)) {
                lazyGetPrivateTemplate({ builtin: false, term: componentObj.part_no }).then((res) => {
                  if (res.error) {
                    setPrivateTemplateId(null);
                  } else if (!_.isEmpty(res.data) && _.find(res.data, t => t.part_no === componentObj.part_no)) {
                    setPrivateTemplateId(_.get(_.find(res.data, t => t.part_no === componentObj.part_no), 'id', null));
                  } else {
                    setPrivateTemplateId(null);
                  }
                });
              } else {
                setPrivateTemplateId(null);
              }
              setSelectionOptionPos({
                x: opt.e.clientX - containerRef.current.getBoundingClientRect().left,
                y: opt.e.clientY - containerRef.current.getBoundingClientRect().top,
                type: 'component',
              });
            }
          });

          curRect.set('stroke', '#56CCF2');

          curRect.onDeselect = (opt) => {
            // console.log('deselect', opt);
            if (!fcanvasRef.current || !curRect) return;
            if (_.isNull(selectedCid) || _.isUndefined(opt.e)) return;
            // check if the mouse is outside of the current object
            const point = fcanvasRef.current.getPointer(opt.e, true);
            if (!curRect.containsPoint(point)) {
              // only reset if deselect outside of the current component
              setSelectedCid(null);
              setSelectedFid(null);
              setSelectedLineItemName('');
              setHoveredCid(null);
              setHoveredFid(null);
              setHoveredLineItemName('');
              curSelectedRectsRef.current = [];
              setSelectedArrayIndex(null);
              setSelectedScope('');
              setSelectedPartNo(null);
              setSelectedPackageNo(null);
            }
          };

          curRect.set('selectable', false);
          curRect.set('evented', true);

          // zoomPanToObject(curRect, fcanvasRef.current);

          curRect.on('moving', () => {
            // console.log('moving');
            if (_.isEmpty(displayedFeaturesRef.current)) return;

            const features = _.filter(displayedFeaturesRef.current, f => f.get('gid') === curRect.get('gid'));
            if (_.isEmpty(features)) return;

            // get delta position based on center
            // we don't round to int here instead we round when submit
            const centerDelta = {
              x: curRect.getCenterPoint().x - curRect.get('originalRotatedCenter').x,
              y: curRect.getCenterPoint().y - curRect.get('originalRotatedCenter').y,
            };

            for (const feature of features) {
              feature.set({
                left: feature.get('originalRotatedCenter').x + centerDelta.x - (feature.get('originalRectInnerDim').width) / 2 - newRectStrokeWidth,
                top: feature.get('originalRotatedCenter').y + centerDelta.y - (feature.get('originalRectInnerDim').height) / 2 - newRectStrokeWidth,
                angle: 0,
              });
              feature.setCoords();
              feature.rotate(_.get(feature.get('featureObj'), 'roi.angle', 0)); // reset rotation
            }

            updateZIndex(selectedCid, selectedFid, selectedLineItemName, selectedUngroupedFid, selectedAgentParam);
          });

          curRect.on('rotating', () => {
            // console.log('rotating');
            if (_.isEmpty(displayedFeaturesRef.current)) return;
            const features = _.filter(displayedFeaturesRef.current, f => f.get('gid') === curRect.get('gid'));
            if (_.isEmpty(features)) return;

            const rotationDelta = curRect.angle - curRect.get('originalAng');

            const componentRectCenter = getComponentCenterByRoiDtoObj(curRect.get('componentObj').shape);

            // apply new translation and rotation but based on the center of the component to all features
            for (const f of features) {
              // get cur feature's original center
              let featureCenter = getComponentCenterByRoiDtoObj(_.get(f.get('featureObj'), 'roi', {}));
              featureCenter = rotatePoint(featureCenter, rotationDelta, componentRectCenter);

              f.set({
                left: featureCenter.x - (_.get(f.get('featureObj'), 'roi.points[1].x', 0) - _.get(f.get('featureObj'), 'roi.points[0].x', 0) + 1) / 2 - newRectStrokeWidth,
                top: featureCenter.y - (_.get(f.get('featureObj'), 'roi.points[1].y', 0) - _.get(f.get('featureObj'), 'roi.points[0].y', 0) + 1) / 2 - newRectStrokeWidth,
                // width: _.get(f.get('featureObj'), 'roi.points[1].x', 0) - _.get(f.get('featureObj'), 'roi.points[0].x', 0) + 1 + newRectStrokeWidth,
                // height: _.get(f.get('featureObj'), 'roi.points[1].y', 0) - _.get(f.get('featureObj'), 'roi.points[0].y', 0) + 1 + newRectStrokeWidth,
                angle: 0,
              });
              f.setCoords();
              f.rotate(_.get(f.get('featureObj'), 'roi.angle', 0) + rotationDelta);
            }

            updateZIndex(selectedCid, selectedFid, selectedLineItemName, selectedUngroupedFid, selectedAgentParam);
          });

          curRect.on('modified', () => {
            const features = _.filter(displayedFeaturesRef.current, f => f.get('gid') === curRect.get('gid'));

            const run = async (curRect, features, component, selectedArrayIndex) => {
              dispatch(setIsContainerLvlLoadingEnabled(true));
              dispatch(setContainerLvlLoadingMsg(t('loader.updatingComponent')));

              // update features and component
              const isRotated = curRect.angle !== curRect.get('originalAng');

              const newFeatureObjs = [];

              if (isRotated) {
                // rotation only
                // submit
                if (!_.isEmpty(features)) {
                  // submit original position
                  for (const f of features) {
                    let featureCenter = _.cloneDeep(f.getCenterPoint());
                    // console.log('featureCenter', featureCenter);
                    // featureCenter = rotatePoint(featureCenter, curRect.angle-_.get(component, 'shape.angle', 0), componentCenter);
                    // console.log('rotated featureCenter', featureCenter);
                    const shape = {
                      type: 'obb',
                      points: [
                        {
                          x: _.round(featureCenter.x - f.get('originalRectInnerDim').width / 2, 0),
                          y: _.round(featureCenter.y - f.get('originalRectInnerDim').height / 2, 0),
                        },
                        {
                          x: _.round(featureCenter.x + f.get('originalRectInnerDim').width / 2 - 1, 0),
                          y: _.round(featureCenter.y + f.get('originalRectInnerDim').height / 2 - 1, 0),
                        }
                      ],
                      angle: _.get(f.get('featureObj'), 'roi.angle', 0) + (curRect.angle - _.get(component, 'shape.angle', 0)),
                      center: null,
                    };

                    const res = await updateFeatureRoi({
                      body: {
                        ...f.get('featureObj'),
                        roi: shape,
                      },
                      params: { allComponents: false },
                    });

                    if (res.error) {
                      aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
                      console.error(res.error.message);
                      dispatch(setIsContainerLvlLoadingEnabled(false));
                      dispatch(setContainerLvlLoadingMsg(''));
                      return;
                    }

                    // newFeatureObjs.push({
                    //   ...f.get('featureObj'),
                    //   roi: shape,
                    // });
                    if (!_.isEmpty(res.data)) {
                      newFeatureObjs.push(..._.map(res.data, fObj => ({
                        ...fObj,
                        line_item_params: f.get('featureObj').line_item_params,
                      })));
                    }
                  }
                }

                // rotated so use original position
                const shape = {
                  type: 'obb',
                  points: _.get(component, 'shape.points', []),
                  angle: curRect.angle,
                  center: null,
                };

                const payload = {
                  ...curRect.get('componentObj'),
                  shape,
                  center: {
                    x: curRect.get('originalRectTopLeftWithZeroRotation').left + curRect.get('originalRectInnerDim').width / 2 + curRect.strokeWidth,
                    y: curRect.get('originalRectTopLeftWithZeroRotation').top + curRect.get('originalRectInnerDim').height / 2 + curRect.strokeWidth,
                  },
                  // all: false, // only scaling(caused by feature/agent param roi update, not the component rect itself) on component should be sync to all components in the group
                };

                delete payload['color_map_uri'];
                delete payload['depth_map_uri'];
                delete payload['created_at'];
                delete payload['modified_at'];
                delete payload['can_group_by_package_no'];
                delete payload['can_group_by_part_no'];
                delete payload['array_index'];
                delete payload['cloned'];
                delete payload['designator'];
                delete payload['variation_for'];

                const res = await updateComponent({
                  body: payload,
                  params: { allComponents: false },
                });

                if (res.error) {
                  aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
                  console.error(res.error.message);
                  dispatch(setIsContainerLvlLoadingEnabled(false));
                  dispatch(setContainerLvlLoadingMsg(''));
                  return;
                }
              } else {
                // translation only
                // get position delta and rounded it to int
                // which will cause minor translation after component/feature is reloaded
                const centerDelta = {
                  x: _.round(curRect.getCenterPoint().x - curRect.get('originalRotatedCenter').x, 0),
                  y: _.round(curRect.getCenterPoint().y - curRect.get('originalRotatedCenter').y, 0),
                };

                // submit
                if (!_.isEmpty(features)) {
                  for (const f of features) {
                    const fAng = f.angle;

                    const shape = {
                      type: 'obb',
                      // we need the inner pmin and pmax
                      points: [
                        {
                          x: _.get(f.get('featureObj'), 'roi.points[0].x', 0) + centerDelta.x,
                          y: _.get(f.get('featureObj'), 'roi.points[0].y', 0) + centerDelta.y,
                        },
                        {
                          x: _.get(f.get('featureObj'), 'roi.points[1].x', 0) + centerDelta.x,
                          y: _.get(f.get('featureObj'), 'roi.points[1].y', 0) + centerDelta.y,
                        }
                      ],
                      angle: fAng,
                      center: null,
                    };

                    const res = await updateFeatureRoi({
                      body: {
                        ...f.get('featureObj'),
                        roi: shape,
                      },
                      params: { allComponents: false },
                    });

                    if (res.error) {
                      aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
                      console.error(res.error.message);
                      dispatch(setIsContainerLvlLoadingEnabled(false));
                      dispatch(setContainerLvlLoadingMsg(''));
                      return;
                    }

                    if (!_.isEmpty(res.data)) {
                      newFeatureObjs.push(...res.data);
                    }
                  }
                }

                const shape = {
                  type: 'obb',
                  points: [
                    {
                      x: curRect.get('originalRectTopLeftWithZeroRotation').left + centerDelta.x + curRect.strokeWidth,
                      y: curRect.get('originalRectTopLeftWithZeroRotation').top + centerDelta.y + curRect.strokeWidth,
                    },
                    {
                      x: curRect.get('originalRectTopLeftWithZeroRotation').left + centerDelta.x + curRect.get('originalRectInnerDim').width + curRect.strokeWidth - 1,
                      y: curRect.get('originalRectTopLeftWithZeroRotation').top + centerDelta.y + curRect.get('originalRectInnerDim').height + curRect.strokeWidth - 1,
                    },
                  ],
                  angle: curRect.get('angle'),
                  center: null
                };

                const payload = {
                  ...curRect.get('componentObj'),
                  shape,
                  feature_ids: _.map(features, f => f.get('fid')),
                  center: {
                    x: curRect.get('originalRectTopLeftWithZeroRotation').left + centerDelta.x + curRect.get('originalRectInnerDim').width / 2 + curRect.strokeWidth,
                    y: curRect.get('originalRectTopLeftWithZeroRotation').top + centerDelta.y + curRect.get('originalRectInnerDim').height / 2 + curRect.strokeWidth,
                  }
                };

                delete payload['color_map_uri'];
                delete payload['depth_map_uri'];
                delete payload['created_at'];
                delete payload['modified_at'];
                delete payload['can_group_by_package_no'];
                delete payload['can_group_by_part_no'];
                delete payload['array_index'];
                delete payload['cloned'];
                delete payload['designator'];
                delete payload['variation_for'];

                const res = await updateComponent({
                  body: payload,
                  params: { allComponents: false },
                });

                if (res.error) {
                  aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
                  console.error(res.error.message);
                  dispatch(setIsContainerLvlLoadingEnabled(false));
                  dispatch(setContainerLvlLoadingMsg(''));
                  return;
                }
              }

              // order matters here ensure components get reloaded first
              await refetchAllComponents();
              // 2025/07/04 call get all features by component id directly
              const res2 = await lazyGetAllFeatures({
                product_id: Number(_.get(curProduct, 'product_id', 0)),
                step: 0,
                variant: _.get(curProduct, 'product_name', ''),
                marker: false,
                component_id: selectedCid,
              });

              if (res2.error) {
                aoiAlert(t('notification.error.getAllFeatures'), ALERT_TYPES.COMMON_ERROR);
                console.error(res2.error.message);
              }

              await updateAllFeaturesState(_.map(res2.data, 'feature_id'), 'update', res2.data);

              // await updateAllFeaturesState(_.map(newFeatureObjs, 'feature_id'), updateFeatureInGroup, newFeatureObjs);

              dispatch(setIsContainerLvlLoadingEnabled(false));
              dispatch(setContainerLvlLoadingMsg(''));
            };

            run(curRect, features, c, selectedArrayIndex);
          });
        } else if (_.isInteger(selectedCid) && _.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
          curRect.set('selectable', false);
          curRect.set('evented', false);
          curRect.set('stroke', 'white');
        }
      }

      fcanvasRef.current.add(curRect);
      displayedComponentBoxRef.current.push(curRect);
    }
    updateZIndex(selectedCid, selectedFid, selectedLineItemName, selectedUngroupedFid, selectedAgentParam);
  };

  const handleUngroupedFeatureRectUpdate = async ({
    feature,
    curRect,
  }) => {
    let newFeature;

    const curFeatureCenter = curRect.getCenterPoint();

    if (curRect.get('scaleX') !== 1 || curRect.get('scaleY') !== 1) {
      // scaling
      const newFeatureRectInnerDimension = {
        width: (curRect.get('originalRectInnerDim').width + newRectStrokeWidth) * curRect.get('scaleX') - newRectStrokeWidth,
        height: (curRect.get('originalRectInnerDim').height + newRectStrokeWidth) * curRect.get('scaleY') - newRectStrokeWidth,
      };

      newFeature = {
        ...feature,
        roi: {
          ...feature.roi,
          points: [
            {
              x: _.round(curFeatureCenter.x - newFeatureRectInnerDimension.width / 2, 0),
              y: _.round(curFeatureCenter.y - newFeatureRectInnerDimension.height / 2, 0),
            },
            {
              x: _.round(curFeatureCenter.x + newFeatureRectInnerDimension.width / 2 - 1, 0),
              y: _.round(curFeatureCenter.y + newFeatureRectInnerDimension.height / 2 - 1, 0),
            }
          ]
        }
      };
    } else if (curRect.get('angle') !== _.get(feature, 'roi.angle', 0)) {
      // rotation
      newFeature = {
        ...feature,
        roi: {
          ...feature.roi,
          angle: curRect.get('angle'),
        }
      };
    } else {
      // translation
      newFeature = {
        ...feature,
        roi: {
          ...feature.roi,
          points: [
            {
              x: _.round(curFeatureCenter.x - curRect.get('originalRectInnerDim').width / 2, 0),
              y: _.round(curFeatureCenter.y - curRect.get('originalRectInnerDim').height / 2, 0),
            },
            {
              x: _.round(curFeatureCenter.x + curRect.get('originalRectInnerDim').width / 2 - 1, 0),
              y: _.round(curFeatureCenter.y + curRect.get('originalRectInnerDim').height / 2 - 1, 0),
            }
          ]
        }
      };
    }

    const res = await updateFeatureRoi({ body: newFeature, params: { allComponents: false } });

    if (res.error) {
      aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
    }

    // await updateAllFeaturesState([feature.feature_id], 'update', [newFeature]);
    if (!_.isEmpty(res.data)) {
      await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
    }

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  const delayUpdateUngroupFeatureRect = _.debounce(({
    feature,
    curRect,
  }) => {
    handleUngroupedFeatureRectUpdate({ feature, curRect });
  }, keyboardSubmitInterval);

  const handleComponentRectUpdate = async ({
    curRect,
    component,
  }) => {
    const features = _.filter(displayedFeaturesRef.current, f => f.get('gid') === curRect.get('gid'));
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.updatingComponent')));

    // update features and component
    const isRotated = curRect.angle !== curRect.get('originalAng');

    const newFeatureObjs = [];

    if (isRotated) {
      // rotation only
      const componentCenter = getComponentCenterByRoiDtoObj(_.get(component, 'shape', {}));
      if (!_.isEmpty(features)) {
        // submit original position
        for (const f of features) {
          let featureCenter = _.cloneDeep(f.getCenterPoint());
          // console.log('featureCenter', featureCenter);
          featureCenter = rotatePoint(featureCenter, curRect.angle-_.get(component, 'shape.angle', 0), componentCenter);
          // console.log('rotated featureCenter', featureCenter);
          const shape = {
            type: 'obb',
            points: [
              {
                x: _.round(featureCenter.x - f.get('originalRectInnerDim').width / 2, 0),
                y: _.round(featureCenter.y - f.get('originalRectInnerDim').height / 2, 0),
              },
              {
                x: _.round(featureCenter.x + f.get('originalRectInnerDim').width / 2 - 1, 0),
                y: _.round(featureCenter.y + f.get('originalRectInnerDim').height / 2 - 1, 0),
              }
            ],
            angle: _.get(f.get('featureObj'), 'roi.angle', 0) + (curRect.angle - _.get(component, 'shape.angle', 0)),
            center: null,
          };

          const res = await updateFeatureRoi({
            body: {
              ...f.get('featureObj'),
              roi: shape,
            },
            params: { allComponents: false },
          });

          if (res.error) {
            aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
            console.error(res.error.message);
            dispatch(setIsContainerLvlLoadingEnabled(false));
            dispatch(setContainerLvlLoadingMsg(''));
            return;
          }

          // newFeatureObjs.push({
          //   ...f.get('featureObj'),
          //   roi: shape,
          // });
          if (!_.isEmpty(res.data)) {
            newFeatureObjs.push(..._.map(res.data, f => ({
              ...f,
              line_item_params: f.get('featureObj').line_item_params,
            })));
          }
        }
      }

      // rotated so use original position
      const shape = {
        type: 'obb',
        points: _.get(component, 'shape.points', []),
        angle: curRect.angle,
        center: null,
      };

      const payload = {
        ...curRect.get('componentObj'),
        shape,
        center: {
          x: curRect.get('originalRectTopLeftWithZeroRotation').left + curRect.get('originalRectInnerDim').width / 2 + curRect.strokeWidth,
          y: curRect.get('originalRectTopLeftWithZeroRotation').top + curRect.get('originalRectInnerDim').height / 2 + curRect.strokeWidth,
        },
        // all: false,
      };

      delete payload['color_map_uri'];
      delete payload['depth_map_uri'];
      delete payload['created_at'];
      delete payload['modified_at'];
      delete payload['can_group_by_package_no'];
      delete payload['can_group_by_part_no'];
      delete payload['array_index'];
      delete payload['cloned'];
      delete payload['designator'];
      delete payload['variation_for'];

      const res = await updateComponent({
        body: payload,
        params: { allComponents: false },
      });

      if (res.error) {
        aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
        console.error(res.error.message);
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }
    } else {
      // translation only
      // get position delta and rounded it to int
      // which will cause minor translation after component/feature is reloaded
      const centerDelta = {
        x: _.round(curRect.getCenterPoint().x - curRect.get('originalRotatedCenter').x, 0),
        y: _.round(curRect.getCenterPoint().y - curRect.get('originalRotatedCenter').y, 0),
      };

      // submit
      if (!_.isEmpty(features)) {
        for (const f of features) {
          const fAng = f.angle;

          const shape = {
            type: 'obb',
            // we need the inner pmin and pmax
            points: [
              {
                x: _.get(f.get('featureObj'), 'roi.points[0].x', 0) + centerDelta.x,
                y: _.get(f.get('featureObj'), 'roi.points[0].y', 0) + centerDelta.y,
              },
              {
                x: _.get(f.get('featureObj'), 'roi.points[1].x', 0) + centerDelta.x,
                y: _.get(f.get('featureObj'), 'roi.points[1].y', 0) + centerDelta.y,
              }
            ],
            angle: fAng,
            center: null,
          };

          const res = await updateFeatureRoi({
            body: {
              ...f.get('featureObj'),
              roi: shape,
            },
            params: { allComponents: false },
          });

          if (res.error) {
            aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
            console.error(res.error.message);
            dispatch(setIsContainerLvlLoadingEnabled(false));
            dispatch(setContainerLvlLoadingMsg(''));
            return;
          }

          // newFeatureObjs.push({
          //   ...f.get('featureObj'),
          //   roi: shape,
          // });
          if (!_.isEmpty(res.data)) {
            newFeatureObjs.push(..._.map(res.data, f => ({
              ...f,
              line_item_params: f.get('featureObj').line_item_params,
            })));
          }
        }
      }

      const shape = {
        type: 'obb',
        points: [
          {
            x: curRect.get('originalRectTopLeftWithZeroRotation').left + centerDelta.x + curRect.strokeWidth,
            y: curRect.get('originalRectTopLeftWithZeroRotation').top + centerDelta.y + curRect.strokeWidth,
          },
          {
            x: curRect.get('originalRectTopLeftWithZeroRotation').left + centerDelta.x + curRect.get('originalRectInnerDim').width + curRect.strokeWidth - 1,
            y: curRect.get('originalRectTopLeftWithZeroRotation').top + centerDelta.y + curRect.get('originalRectInnerDim').height + curRect.strokeWidth - 1,
          },
        ],
        angle: curRect.get('angle'),
        center: null,
      };

      const payload = {
        ...curRect.get('componentObj'),
        shape,
        feature_ids: _.map(features, f => f.get('fid')),
        center: {
          x: curRect.get('originalRectTopLeftWithZeroRotation').left + centerDelta.x + curRect.get('originalRectInnerDim').width / 2 + curRect.strokeWidth,
          y: curRect.get('originalRectTopLeftWithZeroRotation').top + centerDelta.y + curRect.get('originalRectInnerDim').height / 2 + curRect.strokeWidth,
        },
        // all: false, // only scaling(caused by feature/agent param roi update, not the component rect itself) on component should be sync to all components in the group
      };

      delete payload['color_map_uri'];
      delete payload['depth_map_uri'];
      delete payload['created_at'];
      delete payload['modified_at'];
      delete payload['can_group_by_package_no'];
      delete payload['can_group_by_part_no'];
      delete payload['array_index'];
      delete payload['cloned'];
      delete payload['designator'];
      delete payload['variation_for'];

      const res = await updateComponent({
        body: payload,
        params: { allComponents: false },
      });

      if (res.error) {
        aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
        console.error(res.error.message);
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }
    }

    // order matters here ensure components get reloaded first
    await refetchAllComponents();
    // await refetchAllFeatures();
    await updateAllFeaturesState(_.map(features, f => f.get('fid')), updateFeatureInGroup, newFeatureObjs);

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  const delayUpdateComponent = _.debounce(async ({
    curRect,
    component,
  }) => {
    handleComponentRectUpdate({
      curRect,
      component,
    });
  }, keyboardSubmitInterval);

  const handleFeatureRectUpdate = async ({
    allComponents,
    curRect,
    allFeatures,
    selectedFid,
    selectedCid,
    selectedArrayIndex,
  }) => {
    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.updatingFeature')));

    const component = _.find(allComponents, c => c.region_group_id === selectedCid && c.array_index === selectedArrayIndex);
    const feature = _.find(allFeatures, f => f.feature_id === selectedFid && f.array_index === selectedArrayIndex);
    const features = _.filter(allFeatures, f => f.group_id === selectedCid && f.array_index === selectedArrayIndex);

    if (_.isUndefined(component) || _.isUndefined(feature)) return;

    const prevComponentCenter = getComponentCenterByRoiDtoObj(_.get(component, 'shape', {}));

    const curFeatureCenter = curRect.getCenterPoint();
    let newFeature;

    if (curRect.get('scaleX') !== 1 || curRect.get('scaleY') !== 1) {
      // scaling
      const newFeatureRectInnerDimension = {
        width: (curRect.get('originalRectInnerDim').width + newRectStrokeWidth) * curRect.get('scaleX') - newRectStrokeWidth,
        height: (curRect.get('originalRectInnerDim').height + newRectStrokeWidth) * curRect.get('scaleY') - newRectStrokeWidth,
      };

      newFeature = {
        ...feature,
        roi: {
          ...feature.roi,
          points: [
            {
              x: _.round(curFeatureCenter.x - newFeatureRectInnerDimension.width / 2, 0),
              y: _.round(curFeatureCenter.y - newFeatureRectInnerDimension.height / 2, 0),
            },
            {
              x: _.round(curFeatureCenter.x + newFeatureRectInnerDimension.width / 2 - 1, 0),
              y: _.round(curFeatureCenter.y + newFeatureRectInnerDimension.height / 2 - 1, 0),
            }
          ]
        }
      };

      if (curRect.get('scaleX') < 1 || curRect.get('scaleY') < 1) {
        // update polarity and mask roi when scale down
        // this could cause polarity or mask roi out of bound
        newFeature = updatePolarityNMaskROIByFeatureScalingEvent(newFeature);
      }

      if (isSolderFeatureAndProfileRoiEnabled(feature) && isAOI3DSMT) {
        newFeature = {
          ...newFeature,
          line_item_params: {
            [solderInspection3D]: {
              ..._.get(feature, `line_item_params.${solderInspection3D}`, {}),
              params: {
                ..._.get(feature, `line_item_params.${solderInspection3D}.params`, {}),
                [profileHeight]: {
                  ..._.get(feature, `line_item_params.${solderInspection3D}.params.${profileHeight}`, {}),
                  param_int: {
                    ..._.get(feature, `line_item_params.${solderInspection3D}.params.${profileHeight}.param_int`, {}),
                    value: _.floor(_.get(newFeature, `roi.points[1].y`, 0) - _.get(newFeature, `roi.points[0].y`, 0) + 1 +
                    _.get(feature, `line_item_params.${solderInspection3D}.params.${extTop}.param_int.value`, 0) +
                    _.get(feature, `line_item_params.${solderInspection3D}.params.${extBottom}.param_int.value`, 0), 0),
                  },
                },
              }
            },
          },
        };

        if (curRect.get('scaleX') < 1) {
          // also update the profile width to min(feature roi width, profile width)
          newFeature = {
            ...newFeature,
            line_item_params: {
              [solderInspection3D]: {
                ..._.get(feature, `line_item_params.${solderInspection3D}`, {}),
                params: {
                  ..._.get(feature, `line_item_params.${solderInspection3D}.params`, {}),
                  [profileWidth]: {
                    ..._.get(feature, `line_item_params.${solderInspection3D}.params.${profileWidth}`, {}),
                    param_int: {
                      ..._.get(feature, `line_item_params.${solderInspection3D}.params.${profileWidth}.param_int`, {}),
                      value: _.min([
                        _.get(newFeature, `roi.points[1].x`, 0) - _.get(newFeature, `roi.points[0].x`, 0) + 1,
                        _.get(feature, `line_item_params.${solderInspection3D}.params.${profileWidth}.param_int.value`, 0),
                      ])
                    },
                  },
                }
              },
            },
          };
        }
      }
    } else if (curRect.get('angle') !== _.get(feature, 'roi.angle', 0)) {
      // rotation
      newFeature = {
        ...feature,
        roi: {
          ...feature.roi,
          angle: curRect.get('angle'),
        }
      };
    } else {
      // translation
      newFeature = {
        ...feature,
        roi: {
          ...feature.roi,
          points: [
            {
              x: _.round(curFeatureCenter.x - curRect.get('originalRectInnerDim').width / 2, 0),
              y: _.round(curFeatureCenter.y - curRect.get('originalRectInnerDim').height / 2, 0),
            },
            {
              x: _.round(curFeatureCenter.x + curRect.get('originalRectInnerDim').width / 2 - 1, 0),
              y: _.round(curFeatureCenter.y + curRect.get('originalRectInnerDim').height / 2 - 1, 0),
            }
          ]
        }
      };
    }

    const updatedFeatures = _.map(features, (f) => {
      if (f.feature_id === feature.feature_id) {
        return newFeature;
      } return f;
    });

    // regenerate the new component center
    const {
      pMin,
      pMax,
      center: newComponentNoRotationCenter
    } = getComponentRectInfoByFeatures(updatedFeatures, component);

    // component's center might have changed so need to find the new component center rotated around the prev center
    const newComponentCenter = rotatePoint(newComponentNoRotationCenter, _.get(component, 'shape.angle', 0), prevComponentCenter);

    const newComponentShape = {
      center: null,
      angle: _.get(component, 'shape.angle', 0),
      points: [
        {
          x: _.round(newComponentCenter.x - (pMax.x - pMin.x + 1) / 2, 0),
          y: _.round(newComponentCenter.y - (pMax.y - pMin.y + 1) / 2, 0),
        },
        {
          x: _.round(newComponentCenter.x + (pMax.x - pMin.x + 1) / 2 - 1, 0),
          y: _.round(newComponentCenter.y + (pMax.y - pMin.y + 1) / 2 - 1, 0),
        }
      ],
      type: 'obb',
    };

    const updateFeatureRes = await updateFeatureRoi({ body: newFeature, params: { allComponents: _.isInteger(newFeature.group_id) } });

    if (updateFeatureRes.error) {
      aoiAlert(t('notification.error.updateFeature'), ALERT_TYPES.COMMON_ERROR);
      console.error(updateFeatureRes.error.message);
      return;
    }

    const cPayload = {
      ...component,
      shape: newComponentShape,
      // all: true,
    };

    delete cPayload['array_index'];
    delete cPayload['cloned'];
    delete cPayload['color_map_uri'];
    delete cPayload['depth_map_uri'];
    delete cPayload['created_at'];
    delete cPayload['modified_at'];
    delete cPayload['can_group_by_package_no'];
    delete cPayload['can_group_by_part_no'];
    delete cPayload['designator'];
    delete cPayload['variation_for'];

    const res = await updateComponent({ body: cPayload, params: { allComponents: true } });

    if (res.error) {
      aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      return;
    }

    await refetchAllComponents();
    // await updateAllFeaturesState([feature.feature_id], 'update', [newFeature]);
    if (!_.isEmpty(updateFeatureRes.data)) {
      await updateAllFeaturesState(_.map(updateFeatureRes.data, 'feature_id'), updateFeatureInGroup, updateFeatureRes.data);
    }
  };

  const delayUpdateFeature = _.debounce(({
    allComponents,
    curRect,
    allFeatures,
    selectedFid,
    selectedCid,
    selectedArrayIndex,
  }) => {
    handleFeatureRectUpdate({
      allComponents,
      curRect,
      allFeatures,
      selectedFid,
      selectedCid,
      selectedArrayIndex,
    });
  }, keyboardSubmitInterval);

  const delayLoadHighSoluScene = useCallback(
    _.debounce(async ({
      fcanvasRef,
      rawImageW,
      rawImageH,
      displayedHighResolSceneRef,
      imageUri,
      depthUri,
    }) => {
      dispatch(setTransparentLoadingEnabled(true));

      await loadHighResolScene({
        fcanvasRef,
        rawImageW,
        rawImageH,
        displayedHighResolSceneRef,
        imageUri,
        depthUri,
        type: 'image',
        callback: () => {
          updateZIndex(selectedCidRef.current, selectedFidRef.current, selectedLineItemNameRef.current, selectedUngroupedFidRef.current, selectedAgentParamRef.current);
        },
      });

      dispatch(setTransparentLoadingEnabled(false));
    }, highResoluRefreshInterval),
  [curImageMetadata]);

  const handleSwitchTool = (tool) => {
    if (!fcanvasRef.current) return;
    if (_.isUndefined(tool) || _.isEmpty(tool)) return;

    setSelectedArrayIndex(null);
    setSelectedCid(null);
    setSelectedFid(null);
    setSelectedLineItemName('');
    setHoveredCid(null);
    setHoveredFid(null);
    setHoveredLineItemName('');
    setSelectedAgentParam(null);
    curSelectedRectsRef.current = [];
    curDrawingRectRef.current = null;

    fcanvasRef.current.discardActiveObject().renderAll();

    fcanvasRef.current.off('mouse:down');
    fcanvasRef.current.off('mouse:move');
    fcanvasRef.current.off('mouse:up');
    fcanvasRef.current.off('mouse:wheel');
    fcanvasRef.current.off('selection:created');
    fcanvasRef.current.selection = false;

    if (globalKeydownEventRef.current) {
      document.removeEventListener('keydown', globalKeydownEventRef.current);
    }

		switch (tool) {
			case 'select':
				// still allow pan and zoom but switch trigger key to middle click
				fcanvasRef.current.on('mouse:down', (opt) => {
					if (opt.e.button === 0) {
						setSelectionOptionPos(null);
					}
					if (opt.e.button === 1) {
						// defaultPanZoomMouseDownEvent.current(opt);
						middlePanZoomMouseDownHandler(fcanvasRef, isPanning);
					} else if (opt.e.button === 2) {
						// right click show options for the selected region
						if (curSelectedRectsRef.current.length > 0) {
							for (const curSelectedRect of curSelectedRectsRef.current) {
								if (
									_.isInteger(
										_.get(curSelectedRect.get('featureObj'), 'group_id', null)
									) ||
                  curSelectedRect.get('componentObj')
								) {
                  return;
                }
							}
							setSelectionOptionPos({
								x:
									opt.e.clientX -
									containerRef.current.getBoundingClientRect().left,
								y:
									opt.e.clientY -
									containerRef.current.getBoundingClientRect().top,
								type: 'featureGroup',
							});
						}
					}
				});
				fcanvasRef.current.on('mouse:move', (opt) => {
					defaultPanZoomMouseMoveEvent.current(opt);
				});
				fcanvasRef.current.on('mouse:up', (opt) => {
					if (opt.e.button === 1) {
						defaultPanZoomMouseUpEvent.current();
						fcanvasRef.current.selection = true;
					}
				});
				fcanvasRef.current.on('mouse:wheel', (opt) => {
					defaultPanZoomMouseWheelEvent.current(opt);
				});
				handleSwitchToSelectMode();
				break;
			case 'transform':
				fcanvasRef.current.on('mouse:down', (opt) => {
					if (opt.e.button === 0) setSelectionOptionPos(null);
					defaultPanZoomMouseDownEvent.current(opt);
				});
				fcanvasRef.current.on(
					'mouse:move',
					defaultPanZoomMouseMoveEvent.current
				);
				fcanvasRef.current.on('mouse:up', defaultPanZoomMouseUpEvent.current);
				fcanvasRef.current.on('mouse:wheel', (opt) => {
					defaultPanZoomMouseWheelEvent.current(opt);
				});
        globalKeydownEventRef.current = (e) => {
          // check if mouse is on canvas
          if (!isMouseInCanvasRef.current) return;
          e.stopPropagation();
          e.preventDefault();
          // pan 1 pixel based on direction keydown(up, down, left, right, w, a, s, d)
          if (e.code === 'KeyW') {
            fcanvasRef.current.relativePan(new fabric.Point(0, 5));
          } else if (e.code === 'KeyS') {
            fcanvasRef.current.relativePan(new fabric.Point(0, -5));
          } else if (e.code === 'KeyA') {
            fcanvasRef.current.relativePan(new fabric.Point(5, 0));
          } else if (e.code === 'KeyD') {
            fcanvasRef.current.relativePan(new fabric.Point(-5, 0));
          }

          if (_.includes([
            'KeyA',
            'KeyD',
            'KeyW',
            'KeyS',
          ], e.code)) {
            delayLoadHighSoluScene({
              fcanvasRef: fcanvasRef,
              rawImageW: _.get(curImageMetadata, 'width', 0),
              rawImageH: _.get(curImageMetadata, 'height', 0),
              displayedHighResolSceneRef: displayedHighResolSceneRef,
              imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
              depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
            });
          }
        }
        document.addEventListener('keydown', globalKeydownEventRef.current);
        break;
			case 'select3DView':
				handleSwitchToDrawMode('select3DView');
				break;
			case 'addBody':
				handleSwitchToDrawMode('addBody');
				break;
			case 'addICLead':
				handleSwitchToDrawMode('addICLead');
				break;
			case 'addSolder':
				handleSwitchToDrawMode('addSolder');
				break;
			case 'addText':
				handleSwitchToDrawMode('addText');
				break;
      case 'addBarcode':
        handleSwitchToDrawMode('addBarcode');
        break;
			default:
				break;
		}
	};

  const handleSwitchToSelectMode = () => {
    if (!fcanvasRef.current) return;
    fcanvasRef.current.discardActiveObject().renderAll();

    fcanvasRef.current.selection = true;

    fcanvasRef.current.forEachObject((obj) => {
      if (obj !== thumbnailBgSceneRef.current && obj !== displayedHighResolSceneRef.current) {
        obj.set({
          selectable: true,
        });
      }
    });

    fcanvasRef.current.on('selection:created', (opt) => {
      if (!_.isEmpty(opt.selected)) {
        curSelectedRectsRef.current = opt.selected;
        const activeSelection = opt.selected[0].group;
        if (!activeSelection) return;
        // activeSelection.set('evented', false);
        activeSelection.setControlsVisibility({
          mt: false,
          mb: false,
          ml: false,
          mr: false,
          tl: false,
          tr: false,
          bl: false,
          br: false,
          mtr: false,
        });
      } else {
        curSelectedRectsRef.current = [];
      }
    });

    fcanvasRef.current.on('selection:cleared', () => {
      curSelectedRectsRef.current = [];
    });

    globalKeydownEventRef.current = (e) => {
      if (!isMouseInCanvasRef.current) return;
      if (!_.includes([
        'KeyA',
        'KeyD',
        'KeyW',
        'KeyS',
      ], e.code)) return;

      // check if mouse is on canvas
      e.stopPropagation();
      e.preventDefault();

      // no rect is selected
      if (!_.isInteger(selectedCidRef.current) && !_.isInteger(selectedFidRef.current) && !_.isInteger(selectedUngroupedFidRef.current)) return;

      const activeRect = fcanvasRef.current.getActiveObject();

      if (!activeRect) return;

      // pan 1 pixel based on direction keydown(w, a, s, d)
      if (e.code === 'KeyW') {
        activeRect.set('top', activeRect.top - 1);
      } else if (e.code === 'KeyS') {
        activeRect.set('top', activeRect.top + 1);
      } else if (e.code === 'KeyA') {
        activeRect.set('left', activeRect.left - 1);
      } else if (e.code === 'KeyD') {
        activeRect.set('left', activeRect.left + 1);
      }

      if (_.includes([
        'KeyA',
        'KeyD',
        'KeyW',
        'KeyS',
      ], e.code)) {
        fcanvasRef.current.renderAll();
        if (_.isInteger(selectedCidRef.current) && _.isInteger(selectedFidRef.current) && !_.isInteger(selectedUngroupedFidRef.current)) {
          delayUpdateFeature({
            allComponents: allComponentsRef.current,
            curRect: activeRect,
            allFeatures: allFeaturesRef.current,
            selectedFid: selectedFidRef.current,
            selectedCid: selectedCidRef.current,
            selectedArrayIndex: selectedArrayIndexRef.current,
          });
        } else if (_.isInteger(selectedCidRef.current) && !_.isInteger(selectedFidRef.current) && !_.isInteger(selectedUngroupedFidRef.current)) {
          delayUpdateComponent({
            curRect: activeRect,
            component: _.find(allComponentsRef.current, c => c.region_group_id === selectedCidRef.current),
          });
        } else if (!_.isInteger(selectedCidRef.current) && _.isInteger(selectedUngroupedFidRef.current) && !_.isInteger(selectedFidRef.current)) {
          delayUpdateUngroupFeatureRect({
            feature: _.find(allFeaturesRef.current, f => f.feature_id === selectedUngroupedFidRef.current),
            curRect: activeRect,
          });
        }
      }
    };

    document.addEventListener('keydown', globalKeydownEventRef.current);

    fcanvasRef.current.renderAll();
  };

  const handleSwitchToDrawMode = (action) => {
    if (!fcanvasRef.current) return;

    // attach draw's mouse events
    // also allow pan and zoom by middle click
    fcanvasRef.current.on('mouse:down', (opt) => {
      if (opt.e.button === 1) {
        // defaultPanZoomMouseDownEvent.current(opt);
        middlePanZoomMouseDownHandler(fcanvasRef, isPanning);
      } else if (opt.e.button === 0) {
        generalDrawRectMouseDownHandle(
          opt,
          fcanvasRef.current,
          (rect) => {
            curDrawingRectRef.current = rect;
            fcanvasRef.current.add(rect);
          }
        );
        const pointer = fcanvasRef.current.getPointer(opt.e);
        drawingInitMousePosRef.current = {
          x: pointer.x,
          y: pointer.y,
        };
        fcanvasRef.current.renderAll();
      }
    });
    fcanvasRef.current.on('mouse:move', (opt) => {
      if (curDrawingRectRef.current) {
        generalDrawRectMouseMoveHandle(opt, fcanvasRef.current, curDrawingRectRef.current, drawingInitMousePosRef.current);
        fcanvasRef.current.renderAll();
      } else {
        defaultPanZoomMouseMoveEvent.current(opt);
      }
    });

    if (action === 'addBody') {
      fcanvasRef.current.on('mouse:up', (opt) => {
        if (opt.e.button === 1) {
          defaultPanZoomMouseUpEvent.current();
        } else if (opt.e.button === 0) {
          // check if the rect length width is more than 0 to avoid submit empty rect
          if (curDrawingRectRef.current.width === 0 || curDrawingRectRef.current.height === 0) {
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          if (curDrawingRectRef.current.width < minFeatureBoxLength || curDrawingRectRef.current.height < minFeatureBoxLength) {
            aoiAlert(applyTemplateString(t('notification.error.featureBoxTooSmall'), { minFeatureBoxLength }), ALERT_TYPES.COMMON_ERROR);
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          // submit add feature
          const payload = {
            step: 0,
            product_id: Number(_.get(curProduct, 'product_id', 0)),
            roi: {
              type: 'obb',
              points: [
                {
                  x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.strokeWidth, 0),
                  y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.strokeWidth, 0),
                },
                {
                  x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.width - 1, 0),
                  y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.height - 1, 0),
                }
              ],
              angle: 0,
              center: null,
            },
            feature_type: `_${_.get(systemMetadata, 'default_component_types[0]', '')}`,
            feature_scope: 'product',
            line_item_params: initMountingDefaultLineItemParams(
              _.get(systemMetadata, 'default_line_items', {}),
              isAOI2DSMT,
              isAOI3DSMT,
            ),
          };

          const submit = async (payload) => {
            const res = await addFeatureRoi({ body: removeProfileHeightFromPayload(payload) });

            if (res.error) {
              aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
              console.error(res.error.message);
              return;
            }

            return res;
          };

          submit(payload).then((res) => {
            // remove curDrawingRect from scene
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            if (res) {
              updateAllFeaturesState(_.map(res.data, 'feature_id'), 'add', _.map(res.data, f => ({
                ...f,
                line_item_params: payload.line_item_params,
              })));
            }
          });
        }
      });
    } else if (action === 'addICLead') {
      fcanvasRef.current.on('mouse:up', (opt) => {
        if (opt.e.button === 1) {
          defaultPanZoomMouseUpEvent.current();
        } else if (opt.e.button === 0) {
          if (curDrawingRectRef.current.width === 0 || curDrawingRectRef.current.height === 0) {
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          if (curDrawingRectRef.current.width < minFeatureBoxLength || curDrawingRectRef.current.height < minFeatureBoxLength) {
            aoiAlert(applyTemplateString(t('notification.error.featureBoxTooSmall'), { minFeatureBoxLength }), ALERT_TYPES.COMMON_ERROR);
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          // submit add feature
          // check if x side is longer than y side then angle = 0 else angle = 270
          // if x side shorter than y side then rotate -270 get the aabb bounding box
          let payload;
          if (curDrawingRectRef.current.width >= curDrawingRectRef.current.height) {
            payload = {
              step: 0,
              product_id: Number(_.get(curProduct, 'product_id', 0)),
              roi: {
                type: 'obb',
                points: [
                  {
                    x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.strokeWidth, 0),
                    y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.strokeWidth, 0),
                  },
                  {
                    x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.width - 1, 0),
                    y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.height - 1, 0),
                  }
                ],
                angle: 0,
                center: null
              },
              feature_type: `_${_.get(systemMetadata, 'default_component_types[1]', '')}`,
              feature_scope: 'product',
              line_item_params: initLeadDefaultLintItemParams(
                _.get(systemMetadata, 'default_line_items', {}),
                isAOI2DSMT,
                isAOI3DSMT,
              ),
            };
          } else {
            const bboxPMinPMax = getRotatedRectBoundingBox(
              {
                x: _.round(curDrawingRectRef.current.left + newRectStrokeWidth, 0),
                y: _.round(curDrawingRectRef.current.top + newRectStrokeWidth, 0),
              },
              {
                x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.width - 1, 0),
                y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.height - 1, 0),
              },
              90
            );
            payload = {
              step: 0,
              product_id: Number(_.get(curProduct, 'product_id', 0)),
              roi: {
                type: 'obb',
                points: [
                  {
                    x: _.round(bboxPMinPMax.pMin.x + newRectStrokeWidth * 2, 0),
                    y: _.round(bboxPMinPMax.pMin.y + newRectStrokeWidth * 2, 0),
                  },
                  {
                    x: _.round(bboxPMinPMax.pMax.x - newRectStrokeWidth, 0),
                    y: _.round(bboxPMinPMax.pMax.y - newRectStrokeWidth, 0),
                  }
                ],
                angle: 270,
                center: null
              },
              feature_type: `_${_.get(systemMetadata, 'default_component_types[1]', '')}`,
              feature_scope: 'product',
              line_item_params: initLeadDefaultLintItemParams(
                _.get(systemMetadata, 'default_line_items', {}),
                isAOI2DSMT,
                isAOI3DSMT,
              ),
            };
          }

          const submit = async (payload) => {
            const res = await addFeatureRoi({ body: removeProfileHeightFromPayload(payload) });

            if (res.error) {
              aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
              console.error(res.error.message);
              return;
            }

            if (_.isEmpty(res.data)) return;

            return res;
          };

          submit(payload).then((res) => {
            // remove curDrawingRect from scene
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            if (res) {
              // updateAllFeaturesState([res.data[0].feature_id], 'add', [{
              //   ...res.data[0],
              //   line_item_params: payload.line_item_params,
              // }]);
              updateAllFeaturesState(_.map(res.data, 'feature_id'), 'add', _.map(res.data, f => ({
                ...f,
                line_item_params: payload.line_item_params,
              })));
            }
          });
        }
      });
    } else if (action === 'addSolder') {
      fcanvasRef.current.on('mouse:up', (opt) => {
        if (opt.e.button === 1) {
          defaultPanZoomMouseUpEvent.current();
        } else if (opt.e.button === 0) {
          if (curDrawingRectRef.current.width === 0 || curDrawingRectRef.current.height === 0) {
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          if (curDrawingRectRef.current.width < minFeatureBoxLength || curDrawingRectRef.current.height < minFeatureBoxLength) {
            aoiAlert(applyTemplateString(t('notification.error.featureBoxTooSmall'), { minFeatureBoxLength }), ALERT_TYPES.COMMON_ERROR);
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          const solderDefaultParams = initSolderDefaultLineItemParams(_.get(systemMetadata, 'default_line_items', {}), isAOI2DSMT);
          let payload;

          if (curDrawingRectRef.current.width >= curDrawingRectRef.current.height) {
            // submit add feature
            const roi = {
              type: 'obb',
              points: [
                {
                  x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.strokeWidth, 0),
                  y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.strokeWidth, 0),
                },
                {
                  x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.width - 1, 0),
                  y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.height - 1, 0),
                }
              ],
              angle: 0,
              center: null
            };
            payload = {
              step: 0,
              product_id: Number(_.get(curProduct, 'product_id', 0)),
              roi,
              feature_type: `_${_.get(systemMetadata, 'default_component_types[3]', '')}`,
              feature_scope: 'product',
            };
            if (isAOI2DSMT) {
              delete solderDefaultParams[solderInspection3D];
              payload.line_item_params = solderDefaultParams;
            } else {
              payload.line_item_params = {
                ...solderDefaultParams,
                [solderInspection3D]: {
                  ...solderDefaultParams[solderInspection3D],
                  params: {
                    ...solderDefaultParams[solderInspection3D].params,
                    // profile_height: {
                    //   ...solderDefaultParams[solderInspection3D].params.profile_height,
                    //   param_int: {
                    //     ...solderDefaultParams[solderInspection3D].params.profile_height.param_int,
                    //     // value: _.round(curDrawingRectRef.current.height - newRectStrokeWidth - 1 +
                    //     value: _.floor(roi.points[1].y - roi.points[0].y + 1 +
                    //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_bottom.param_int.value`, 0) +
                    //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_top.param_int.value`, 0), 0),
                    //   },
                    // },
                    profile_width: {
                      ...solderDefaultParams[solderInspection3D].params.profile_width,
                      param_int: {
                        ...solderDefaultParams[solderInspection3D].params.profile_width.param_int,
                        value: _.round((curDrawingRectRef.current.width - newRectStrokeWidth - 1)/2, 0),
                      },
                    },
                  }
                }
              };
            }
          } else {
            const bboxPMinPMax = getRotatedRectBoundingBox(
              {
                x: _.round(curDrawingRectRef.current.left + newRectStrokeWidth, 0),
                y: _.round(curDrawingRectRef.current.top + newRectStrokeWidth, 0),
              },
              {
                x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.width - 1, 0),
                y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.height - 1, 0),
              },
              90
            );
            const roi = {
              type: 'obb',
              points: [
                {
                  x: _.round(bboxPMinPMax.pMin.x + newRectStrokeWidth * 2, 0),
                  y: _.round(bboxPMinPMax.pMin.y + newRectStrokeWidth * 2, 0),
                },
                {
                  x: _.round(bboxPMinPMax.pMax.x - newRectStrokeWidth, 0),
                  y: _.round(bboxPMinPMax.pMax.y - newRectStrokeWidth, 0),
                }
              ],
              angle: 270,
              center: null
            };

            payload = {
              step: 0,
              product_id: Number(_.get(curProduct, 'product_id', 0)),
              roi,
              feature_type: `_${_.get(systemMetadata, 'default_component_types[3]', '')}`,
              feature_scope: 'product',
            };

            if (isAOI2DSMT) {
              payload.line_item_params = solderDefaultParams;
            } else {
              payload.line_item_params = {
                ...solderDefaultParams,
                [solderInspection3D]: {
                  ...solderDefaultParams[solderInspection3D],
                  params: {
                    ...solderDefaultParams[solderInspection3D].params,
                    // profile_height: {
                    //   ...solderDefaultParams[solderInspection3D].params.profile_height,
                    //   param_int: {
                    //     ...solderDefaultParams[solderInspection3D].params.profile_height.param_int,
                    //     // value: _.round(curDrawingRectRef.current.width - newRectStrokeWidth - 1 +
                    //     value: _.floor(roi.points[1].y - roi.points[0].y + 1 +
                    //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_bottom.param_int.value`, 0) +
                    //       _.get(solderDefaultParams, `${solderInspection3D}.params.ext_top.param_int.value`, 0), 0),
                    //   },
                    // },
                    profile_width: {
                      ...solderDefaultParams[solderInspection3D].params.profile_width,
                      param_int: {
                        ...solderDefaultParams[solderInspection3D].params.profile_width.param_int,
                        // value: _.round((roi.points[1].x - roi.points[0].x - newRectStrokeWidth - 1)/2, 0),
                        value: _.round((curDrawingRectRef.current.width - newRectStrokeWidth - 1)/2, 0),
                      },
                    },
                  }
                }
              };
            }
          }

          const submit = async (payload) => {
            const res = await addFeatureRoi({ body: removeProfileHeightFromPayload(payload) });

            if (res.error) {
              aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
              console.error(res.error.message);
              return;
            }

            return res;
          };

          submit(payload).then((res) => {
            // remove curDrawingRect from scene
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            if (res) {
              // updateAllFeaturesState([res.data[0].feature_id], 'add', [{
              //   ...res.data[0],
              //   line_item_params: payload.line_item_params,
              // }]);
              updateAllFeaturesState(_.map(res.data, 'feature_id'), 'add', _.map(res.data, f => ({
                ...f,
                line_item_params: payload.line_item_params,
              })));
            }
          });
        }
      });
    } else if (action === 'addText') {
      fcanvasRef.current.on('mouse:up', (opt) => {
        if (opt.e.button === 1) {
          defaultPanZoomMouseUpEvent.current();
        } else if (opt.e.button === 0) {
          if (curDrawingRectRef.current.width === 0 || curDrawingRectRef.current.height === 0) {
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          if (curDrawingRectRef.current.width < minFeatureBoxLength || curDrawingRectRef.current.height < minFeatureBoxLength) {
            aoiAlert(applyTemplateString(t('notification.error.featureBoxTooSmall'), { minFeatureBoxLength }), ALERT_TYPES.COMMON_ERROR);
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          // similar to add lead we need to check x or y is the longer side
          // but we set angle to 0 if x side is longer else angle = 90
          let pMin;
          let pMax;

          let cloned;
          curDrawingRectRef.current.clone((clonedRect) => {
            cloned = clonedRect;
          });

          if (curDrawingRectRef.current.width <= curDrawingRectRef.current.height) {
            cloned.rotate(-90);
          }

          cloned.setCoords();
          const aabb = cloned.getBoundingRect();
          pMin = {
            x: aabb.left - newRectStrokeWidth,
            y: aabb.top - newRectStrokeWidth,
          };
          pMax = {
            x: aabb.left + aabb.width,
            y: aabb.top + aabb.height,
          };

          // submit add feature
          const payload = {
            step: 0,
            product_id: Number(_.get(curProduct, 'product_id', 0)),
            roi: {
              type: 'obb',
              points: [
                {
                  x: _.round(pMin.x, 0),
                  y: _.round(pMin.y, 0),
                },
                {
                  x: _.round(pMax.x - 1, 0),
                  y: _.round(pMax.y - 1, 0),
                }
              ],
              angle: curDrawingRectRef.current.width <= curDrawingRectRef.current.height ? 90 : 0,
              center: null
            },
            feature_type: `_${_.get(systemMetadata, 'default_component_types[2]', '')}_${Date.now()}`,
            feature_scope: 'product',
            line_item_params: initTextVerificationDefaultLineItemParams(_.get(systemMetadata, 'default_line_items', {})),
          };

          const submit = async (payload) => {
            // const res = await addFeature({ body: payload, params: { allComponents: false } });
            const res = await addFeatureRoi({ body: removeProfileHeightFromPayload(payload) });

            if (res.error) {
              aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
              console.error(res.error.message);
              return;
            }

            return res;
          };

          submit(payload).then((res) => {
            // remove curDrawingRect from scene
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            // if (res) updateAllFeaturesState([res.data[0].feature_id], 'add', [{
            //   ...res.data[0],
            //   line_item_params: payload.line_item_params,
            // }]);
            if (res.data) {
              updateAllFeaturesState(_.map(res.data, 'feature_id'), 'add', _.map(res.data, f => ({
                ...f,
                line_item_params: payload.line_item_params,
              })));
            }
          });
				}
			});
		} else if (action === 'addBarcode') {
      fcanvasRef.current.on('mouse:up', (opt) => {
        if (opt.e.button === 1) {
          defaultPanZoomMouseUpEvent.current();
        } else if (opt.e.button === 0) {
          if (curDrawingRectRef.current.width === 0 || curDrawingRectRef.current.height === 0) {
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          if (curDrawingRectRef.current.width < minFeatureBoxLength || curDrawingRectRef.current.height < minFeatureBoxLength) {
            aoiAlert(applyTemplateString(t('notification.error.featureBoxTooSmall'), { minFeatureBoxLength }), ALERT_TYPES.COMMON_ERROR);
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

          // submit add feature
          const payload = {
            step: 0,
            product_id: Number(_.get(curProduct, 'product_id', 0)),
            roi: {
              type: 'obb',
              points: [
                {
                  x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.strokeWidth, 0),
                  y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.strokeWidth, 0),
                },
                {
                  x: _.round(curDrawingRectRef.current.left + curDrawingRectRef.current.width - 1, 0),
                  y: _.round(curDrawingRectRef.current.top + curDrawingRectRef.current.height - 1, 0),
                }
              ],
              angle: 0,
              center: null
            },
            feature_type: `_${_.get(systemMetadata, 'default_component_types[4]', '')}`,
            feature_scope: 'product',
            line_item_params: initBarcodeScannerDefaultLineItemParams(_.get(systemMetadata, 'default_line_items', {})),
          };

          const submit = async (payload) => {
            // const res = await addFeature({ body: payload, params: { allComponents: false } });
            const res = await addFeatureRoi({ body: removeProfileHeightFromPayload(payload) });

            if (res.error) {
              aoiAlert(t('notification.error.addFeature'), ALERT_TYPES.COMMON_ERROR);
              console.error(res.error.message);
              return;
            }

            return res;
          };

          submit(payload).then((res) => {
            // remove curDrawingRect from scene
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            // if (res) updateAllFeaturesState([res.data[0].feature_id], 'add', [{
            //   ...res.data[0],
            //   line_item_params: payload.line_item_params,
            // }]);
            if (res.data) {
              updateAllFeaturesState(_.map(res.data, 'feature_id'), 'add', _.map(res.data, f => ({
                ...f,
                line_item_params: payload.line_item_params,
              })));
            }
					});
				}
			});
    } else if (action === 'select3DView') {
			fcanvasRef.current.on('mouse:up', (opt) => {
				if (opt.e.button === 1) {
					defaultPanZoomMouseUpEvent.current();
				} else if (opt.e.button === 0) {
          if (curDrawingRectRef.current.width === 0 || curDrawingRectRef.current.height === 0) {
            fcanvasRef.current.remove(curDrawingRectRef.current);
            curDrawingRectRef.current = null;
            return;
          }

					const submit = () => {
						const pMin = {
							x: _.round(
								curDrawingRectRef.current.left +
									curDrawingRectRef.current.strokeWidth,
								0
							),
							y: _.round(
								curDrawingRectRef.current.top +
									curDrawingRectRef.current.strokeWidth,
								0
							),
						};
						const pMax = {
							x: _.round(
								curDrawingRectRef.current.left +
									curDrawingRectRef.current.width -
									1,
								0
							),
							y: _.round(
								curDrawingRectRef.current.top +
									curDrawingRectRef.current.height -
									1,
								0
							),
						};

						handleSubmitSelect3DArea(pMin, pMax);
						// remove curDrawingRect from scene
						fcanvasRef.current.remove(curDrawingRectRef.current);
						curDrawingRectRef.current = null;
					};

					submit();
				}
			});
		}

    fcanvasRef.current.on('mouse:wheel', (opt) => {
      defaultPanZoomMouseWheelEvent.current(opt);
    });
  };

  const updateZIndex = (
    selectedCid,
    selectedFid,
    selectedLineItemName,
    selectedUngroupedFid,
    selectedAgentParam,
  ) => {
    if (!fcanvasRef.current) return;

    // z-index for template editor viewer
    // thumbnail bg: 1
    // high resol scene: 2
    if (thumbnailBgSceneRef.current) thumbnailBgSceneRef.current.moveTo(1)
    if (displayedHighResolSceneRef.current) displayedHighResolSceneRef.current.moveTo(2);

    // if (!_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedAgentParam) && !_.isInteger(selectedUngroupedFid)) {
    if (!_.isInteger(selectedCid) && !_.isInteger(selectedFid) && !_.isInteger(selectedUngroupedFid)) {
      // when nothing selected
      // all features(grouped) roi rects: 3
      // all component roi rects: all prev objs count
      // all mountings features roi(ungrouped): all prev objs count
      // all other features roi(ungrouped): all prev objs count
      for (const f of displayedFeaturesRef.current) {
        if (_.isInteger(f.get('gid'))) {
          f.moveTo(3);
          f.setCoords();
        }
      }
      const componentZIndex = _.size(displayedFeaturesRef.current) + 2;
      for (const c of displayedComponentBoxRef.current) {
        c.moveTo(componentZIndex);
        c.setCoords();
      }
      const ungroupedMountingZIndex = _.size(displayedComponentBoxRef.current) + componentZIndex;
      let allOtherFeatureZIndex = ungroupedMountingZIndex;
      for (const f of displayedFeaturesRef.current) {
        if (!_.isInteger(f.get('gid')) && _.get(f.get('featureObj'), 'feature_type') === mountingFeatureType) {
          f.moveTo(ungroupedMountingZIndex);
          f.setCoords();
          allOtherFeatureZIndex += 1;
        }
      }
      for (const f of displayedFeaturesRef.current) {
        if (!_.isInteger(f.get('gid')) && _.get(f.get('featureObj'), 'feature_type') !== mountingFeatureType) {
          f.moveTo(allOtherFeatureZIndex);
          f.setCoords();
        }
      }
    // } else if (_.isInteger(selectedCid) && !_.isInteger(selectedFid) && !_.isInteger(selectedUngroupedFid) && _.isEmpty(selectedAgentParam)) {
    } else if (_.isInteger(selectedCid) && !_.isInteger(selectedFid) && !_.isInteger(selectedUngroupedFid)) {
      // when a component is selected
      // all component roi rects: 3
      // all agent displayable obj when only component is selected: all prev objs count
      // all mounting features(within this component) roi rects: all prev objs count
      // all other features(within this component) roi rects: all prev objs count
      for (const c of displayedComponentBoxRef.current) {
        c.moveTo(3);
        c.setCoords();
      }
      const agentRelatedZIndex = _.size(displayedComponentBoxRef.current) + 3;
      for (const a of displayedAgentParamObjsRef.current) {
        a.moveTo(agentRelatedZIndex);
        a.setCoords();
      }
      const mountingFeatureZIndex = _.size(displayedAgentParamObjsRef.current) + agentRelatedZIndex;
      let otherFeatureZIndex = mountingFeatureZIndex;
      for (const f of displayedFeaturesRef.current) {
        if (_.get(f.get('featureObj'), 'feature_type') === mountingFeatureType) {
          f.moveTo(mountingFeatureZIndex);
          f.setCoords();
          otherFeatureZIndex += 1;
        }
      }
      for (const f of displayedFeaturesRef.current) {
        if (_.get(f.get('featureObj'), 'feature_type') !== mountingFeatureType) {
          f.moveTo(otherFeatureZIndex);
          f.setCoords();
        }
      }
    // } else if ((!_.isInteger(selectedFid) && !_.isInteger(selectedCid) && _.isInteger(selectedUngroupedFid)) && _.isEmpty(selectedAgentParam)) {
    } else if ((!_.isInteger(selectedFid) && !_.isInteger(selectedCid) && _.isInteger(selectedUngroupedFid))) {
      // when a feature(ungrouped) is selected
      // all other feature(ungrouped and grouped) roi rects: all prev objs count
      // all component rects: all prev objs count
      // all extended roi rects: 3
      // all arrows: all prev objs count
      // all extended roi rects: all prev objs count
      // all lead segmentation roi rects: all prev objs count
      // this features(ungrouped) roi rects: all prev objs count
      // all profile(polarity/mask/background) roi rects: all prev objs count
      const allOtherFeatureZIndex = 3;
      let allComponentZIndex = allOtherFeatureZIndex;
      for (const f of displayedFeaturesRef.current) {
        if (f.get('gid') !== selectedUngroupedFid) {
          f.moveTo(allOtherFeatureZIndex);
          f.setCoords();
          allComponentZIndex += 1;
        }
      }

      let arrowsZIndex = allComponentZIndex;
      for (const c of displayedComponentBoxRef.current) {
        c.moveTo(allComponentZIndex);
        c.setCoords();
        arrowsZIndex += 1;
      }

      let extendedRoiZIndex = arrowsZIndex;
      for (const a of displayedAgentParamObjsRef.current) {
        if (a.get('agentParamLabel').includes(directionArrow)) {
          a.moveTo(arrowsZIndex);
          a.setCoords();
          extendedRoiZIndex += 1;
        }
      }
      let leadSegmentationRoiZIndex = extendedRoiZIndex;
      for (const a of displayedAgentParamObjsRef.current) {
        if (a.get('agentParamLabel').includes(extendedRoi)) {
          a.moveTo(extendedRoiZIndex);
          a.setCoords();
          leadSegmentationRoiZIndex += 1;
        }
      }
      let featureRoiZIndex = leadSegmentationRoiZIndex;
      for (const a of displayedAgentParamObjsRef.current) {
        if (a.get('agentParamLabel') === `${leadInspection3D}.${leadSegmentationRects}`) {
          a.moveTo(leadSegmentationRoiZIndex);
          a.setCoords();
          featureRoiZIndex += 1;
        }
      }

      let profileRoiZIndex = featureRoiZIndex;
      for (const f of displayedFeaturesRef.current) {
        // if (!_.isInteger(f.get('gid')) && _.get(f.get('featureObj'), 'feature_type') !== mountingFeatureType) {
        if (!_.isInteger(f.get('gid')) && f.get('fid') === selectedUngroupedFid) {
          f.moveTo(featureRoiZIndex);
          f.setCoords();
          profileRoiZIndex += 1;
        }
      }

      for (const a of displayedAgentParamObjsRef.current) {
        if (
          a.get('agentParamLabel').includes(polarityRoi) ||
          a.get('agentParamLabel').includes(maskRoi) ||
          a.get('agentParamLabel').includes(profileRoi) ||
          a.get('agentParamLabel').includes(backgroundRoi1) ||
          a.get('agentParamLabel').includes(backgroundRoi2)
        ) {
          a.moveTo(profileRoiZIndex);
          a.setCoords();
        }
      }
    // } else if (_.isInteger(selectedFid) && _.isInteger(selectedCid) && !_.isInteger(selectedUngroupedFid) && _.isEmpty(selectedAgentParam)) {
    } else if (_.isInteger(selectedFid) && _.isInteger(selectedCid) && !_.isInteger(selectedUngroupedFid)) {
      // when a feature(grouped) is selected
      // all component roi rects: 3
      // all arrows: all prev objs count
      // all extended roi rects: all prev objs count
      // all lead segmentation roi rects: all prev objs count
      // all features(within this component) roi rects: all prev objs count
      // all profile(polarity/mask) roi rects: all prev objs count
      let arrowsZIndex = 3;
      for (const c of displayedComponentBoxRef.current) {
        c.moveTo(3);
        c.setCoords();
        arrowsZIndex += 1;
      }
      let extendedRoiZIndex = arrowsZIndex;
      for (const a of displayedAgentParamObjsRef.current) {
        if (_.isString(a.get('agentParamLabel')) && a.get('agentParamLabel').includes(directionArrow)) {
          a.moveTo(arrowsZIndex);
          a.setCoords();
          extendedRoiZIndex += 1;
        }
      }
      let leadSegmentationRoiZIndex = extendedRoiZIndex;
      for (const a of displayedAgentParamObjsRef.current) {
        if (a.get('agentParamLabel').includes(extendedRoi)) {
          a.moveTo(extendedRoiZIndex);
          a.setCoords();
          leadSegmentationRoiZIndex += 1;
        }
      }
      let featureRoiZIndex = leadSegmentationRoiZIndex;
      for (const a of displayedAgentParamObjsRef.current) {
        if (a.get('agentParamLabel') === `${leadInspection3D}.${leadSegmentationRects}`) {
          a.moveTo(leadSegmentationRoiZIndex);
          a.setCoords();
          featureRoiZIndex += 1;
        }
      }
      let profileRoiZIndex = featureRoiZIndex;
      for (const f of displayedFeaturesRef.current) {
        if (_.isInteger(f.get('gid'))) {
          f.moveTo(featureRoiZIndex);
          f.setCoords();
          profileRoiZIndex += 1;
        }
      }
      for (const a of displayedAgentParamObjsRef.current) {
        if (
          a.get('agentParamLabel').includes(polarityRoi) ||
          a.get('agentParamLabel').includes(maskRoi) ||
          a.get('agentParamLabel').includes(profileRoi) ||
          a.get('agentParamLabel').includes(backgroundRoi1) ||
          a.get('agentParamLabel').includes(backgroundRoi2)
        ) {
          a.moveTo(profileRoiZIndex);
          a.setCoords();
          // profileRoiZIndex += 1;
        }
      }
    }

    fcanvasRef.current.requestRenderAll();
  };

  const resetView = () => {
    if (!thumbnailBgSceneRef.current || !fcanvasRef.current) return;

    zoomPanToObject(thumbnailBgSceneRef.current, fcanvasRef.current, 0.01);
  };

  const init = async (curProduct, curImageMetadata, selectedCid) => {
    await loadInitFullSizeThumbnail({
      fcanvas: fcanvasRef.current,
      rawWidth: _.get(curImageMetadata, 'width'),
      rawHeight: _.get(curImageMetadata, 'height'),
      thumbnailBgSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      type: 'image',
    });
    await loadHighResolScene({
      fcanvasRef,
      rawImageH: _.get(curImageMetadata, 'height', 0),
      rawImageW: _.get(curImageMetadata, 'width', 0),
      displayedHighResolSceneRef: displayedHighResolSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      type: 'image',
    });
    updateZIndex(
      selectedCidRef.current,
      selectedFidRef.current,
      selectedLineItemNameRef.current,
      selectedUngroupedFidRef.current,
      selectedAgentParamRef.current,
    );

    // attach mouse events based on selected tool
    handleSwitchTool('transform');

    if (!_.isInteger(selectedCid)) resetView();
  };

  useEffect(() => {
    handleSwitchTool(selectedTool);
  }, [selectedTool]);

  useEffect(() => {
    if (_.isUndefined(curProduct) || _.isUndefined(curImageMetadata)) return;
    if (!viewerContainerRef.current || !canvasElRef.current) return;

    const fcanvas = new fabric.Canvas(canvasElRef.current, {
      // antialias: 'off',
      uniformScaling: false,
      fireRightClick: true,
      fireMiddleClick: true,
      stopContextMenu: true,
      preserveObjectStacking: true,
    });

    fcanvasRef.current = fcanvas;
    fcanvas.setHeight(viewerContainerRef.current.offsetHeight);
    fcanvas.setWidth(viewerContainerRef.current.offsetWidth);

    // fcanvas.on('mouse:over', () => {
    //   console.log('over!!!!!!');
    //   isMouseInCanvasRef.current = true;
    // });

    // fcanvas.on('mouse:out', () => {
    //   console.log('out!!!!!!');
    //   isMouseInCanvasRef.current = false;
    // });

    // set tabindex
    const upperCanvasEl = fcanvas.upperCanvasEl;
    upperCanvasEl.setAttribute('tabindex', 1);
    // upperCanvasEl.tabIndex = 0;
    upperCanvasEl.setAttribute('z-index', 20);
    // upperCanvasEl.focus();

    upperCanvasEl.addEventListener('mouseover', () => {
      isMouseInCanvasRef.current = true;
    });
    upperCanvasEl.addEventListener('mouseout', () => {
      isMouseInCanvasRef.current = false;
    });

    defaultPanZoomMouseDownEvent.current = (opt) => {
      opt.e.preventDefault();
      generalPanZoomMouseDownHandler(opt, fcanvasRef, isPanning);
    };
    defaultPanZoomMouseMoveEvent.current = (opt) => generalPanZoomMouseMoveHandler(opt, fcanvasRef, isPanning);
    defaultPanZoomMouseUpEvent.current = (opt) => {
      generalPanZoomMouseUpHandler(fcanvasRef, isPanning);

      // const activeObj = fcanvasRef.current.getActiveObject();
      // if (!_.isUndefined(activeObj) && !_.isNull(activeObj)) return;

      // delayLoadHighSoluScene({
      //   fcanvasRef: fcanvasRef,
      //   rawImageW: _.get(curImageMetadata, 'width', 0),
      //   rawImageH: _.get(curImageMetadata, 'height', 0),
      //   displayedHighResolSceneRef: displayedHighResolSceneRef,
      //   imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      //   depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      // });
      loadHighResolScene({
        fcanvasRef: fcanvasRef,
        rawImageW: _.get(curImageMetadata, 'width', 0),
        rawImageH: _.get(curImageMetadata, 'height', 0),
        displayedHighResolSceneRef: displayedHighResolSceneRef,
        imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
        depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
        type: 'image',
        callback: () => {
          updateZIndex(selectedCidRef.current, selectedFidRef.current, selectedLineItemNameRef.current, selectedUngroupedFidRef.current, selectedAgentParamRef.current);
        },
      });
    };
    defaultPanZoomMouseWheelEvent.current = (opt) => {
      generalPanZoomMouseWheelHandler(opt, fcanvasRef);
      delayLoadHighSoluScene({
        fcanvasRef: fcanvasRef,
        rawImageW: _.get(curImageMetadata, 'width', 0),
        rawImageH: _.get(curImageMetadata, 'height', 0),
        displayedHighResolSceneRef: displayedHighResolSceneRef,
        imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
        depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
      });
    };

    init(curProduct, curImageMetadata, selectedCid);
  }, [curProduct, curImageMetadata]);

  // Handle canvas resize when componentDetailWidth changes
  useEffect(() => {
    if (!fcanvasRef.current || !viewerContainerRef.current) return;

    // Use setTimeout to ensure DOM has updated after width change
    const timer = setTimeout(() => {
      if (fcanvasRef.current && viewerContainerRef.current) {
        const newWidth = viewerContainerRef.current.offsetWidth;
        const newHeight = viewerContainerRef.current.offsetHeight;

        // Set canvas dimensions
        fcanvasRef.current.setWidth(newWidth);
        fcanvasRef.current.setHeight(newHeight);

        // Also update the underlying canvas elements
        const canvasEl = fcanvasRef.current.getElement();
        const upperCanvasEl = fcanvasRef.current.upperCanvasEl;
        const lowerCanvasEl = fcanvasRef.current.lowerCanvasEl;

        if (canvasEl) {
          canvasEl.width = newWidth;
          canvasEl.height = newHeight;
          canvasEl.style.width = newWidth + 'px';
          canvasEl.style.height = newHeight + 'px';
        }

        if (upperCanvasEl) {
          upperCanvasEl.width = newWidth;
          upperCanvasEl.height = newHeight;
          upperCanvasEl.style.width = newWidth + 'px';
          upperCanvasEl.style.height = newHeight + 'px';
        }

        if (lowerCanvasEl) {
          lowerCanvasEl.width = newWidth;
          lowerCanvasEl.height = newHeight;
          lowerCanvasEl.style.width = newWidth + 'px';
          lowerCanvasEl.style.height = newHeight + 'px';
        }

        // Force canvas container to match dimensions
        const canvasContainer = fcanvasRef.current.wrapperEl;
        if (canvasContainer) {
          canvasContainer.style.width = newWidth + 'px';
          canvasContainer.style.height = newHeight + 'px';
        }

        // Force re-render and recalculate
        fcanvasRef.current.calcOffset();
        fcanvasRef.current.renderAll();

        // Trigger a manual resize event on the canvas
        setTimeout(() => {
          if (fcanvasRef.current) {
            fcanvasRef.current.setDimensions({
              width: newWidth,
              height: newHeight
            });
            fcanvasRef.current.calcOffset();
            fcanvasRef.current.renderAll();
          }
        }, 10);

        // Load high resolution image after canvas resize
        if (curProduct && curImageMetadata) {
          delayLoadHighSoluScene({
            fcanvasRef: fcanvasRef,
            rawImageW: _.get(curImageMetadata, 'width', 0),
            rawImageH: _.get(curImageMetadata, 'height', 0),
            displayedHighResolSceneRef: displayedHighResolSceneRef,
            imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
            depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
          });
        }
      }
    }, 0);

    return () => clearTimeout(timer);
  }, [componentDetailWidth, curProduct, curImageMetadata, delayLoadHighSoluScene]);

  // ensure the component reload useefect is before the feature reload's one
  useEffect(() => {
    if (!fcanvasRef.current) return;

    if (!_.isEmpty(displayedComponentBoxRef.current)) {
      for (const componentBox of displayedComponentBoxRef.current) {
        fcanvasRef.current.remove(componentBox);
      }
      displayedComponentBoxRef.current = [];
    }

    if (_.isUndefined(allComponents) || _.isEmpty(allComponents)) return;

    if (!_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
      // load all components
      loadComponentsToScene(
        allComponents,
        selectedCid,
        selectedFid,
        selectedLineItemName,
        selectedUngroupedFid,
        selectedAgentParam,
        curProduct,
        selectedArrayIndex,
      );
    } else if (_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
      // load selected component only and all features of the component
      const selectedComponent = _.filter(allComponents, c => c.region_group_id === selectedCid && c.array_index === selectedArrayIndex);
      if (!selectedComponent) return;
      loadComponentsToScene(
        selectedComponent,
        selectedCid,
        selectedFid,
        selectedLineItemName,
        selectedUngroupedFid,
        selectedAgentParam,
        curProduct,
        selectedArrayIndex,
      );
    } else if (_.isInteger(selectedCid) && _.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
      // load selected component and selected feature
      const selectedComponent = _.filter(allComponents, c => c.region_group_id === selectedCid && c.array_index === selectedArrayIndex);
      if (!selectedComponent) return;
      loadComponentsToScene(
        selectedComponent,
        selectedCid,
        selectedFid,
        selectedLineItemName,
        selectedUngroupedFid,
        selectedAgentParam,
        curProduct,
        selectedArrayIndex,
      );
    }

    updateZIndex(selectedCid, selectedFid, selectedLineItemName, selectedUngroupedFid, selectedAgentParam);

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  }, [
    allComponents,
    selectedFid,
    selectedCid,
    selectedLineItemName,
    selectedTool,
    selectedUngroupedFid,
    selectedArrayIndex,
  ]);

  useEffect(() => {
    if (!fcanvasRef.current) return;
    console.log('re-render all feauture start time: ', new Date().getTime());

    if (!_.isEmpty(displayedFeaturesRef.current)) {
      for (const feature of displayedFeaturesRef.current) {
        if (feature.get('extendedRoi')) {
          fcanvasRef.current.remove(feature.get('extendedRoi'));
        }
        if (feature.get('featureDirection')) {
          fcanvasRef.current.remove(feature.get('featureDirection').triangle);
        }
        if (feature.get('leadSegmentation')) {
          _.forEach(feature.get('leadSegmentation'), (leadRect) => {
            fcanvasRef.current.remove(leadRect);
          });
        }
        fcanvasRef.current.remove(feature);
      }
      displayedFeaturesRef.current = [];
    }

    if (!_.isEmpty(displayedAgentParamObjsRef.current)) {
      for (const agentParamObj of displayedAgentParamObjsRef.current) {
        fcanvasRef.current.remove(agentParamObj);
      }
      displayedAgentParamObjsRef.current = [];
    }

    if (_.isUndefined(allFeatures) || _.isEmpty(allFeatures)) return;

    let selectedFeatures = [];

    if (!_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
      // load all features
      selectedFeatures = allFeatures;
    } else if (_.isInteger(selectedCid) && !_.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
      selectedFeatures = _.filter(allFeatures, f => f.group_id === selectedCid && selectedArrayIndex === f.array_index);
      if (_.isEmpty(selectedFeatures)) return;
    } else if (_.isInteger(selectedCid) && _.isInteger(selectedFid) && _.isEmpty(selectedLineItemName)) {
      // load all line item special display(arrow, ext roi, segmentation)
      // and the selected feature
      selectedFeatures = _.filter(allFeatures, f => f.group_id === selectedCid && f.feature_id === selectedFid && f.array_index === selectedArrayIndex);
      if (_.isEmpty(selectedFeatures)) return;
      // zoomPanToObject(_.find(displayedFeaturesRef.current, f => f.get('gid') === selectedCid && f.get('fid') === selectedFid), fcanvasRef.current);
    }

    loadFeaturesToScene(
      selectedFeatures,
      selectedFid,
      selectedCid,
      selectedLineItemName,
      allComponents,
      selectedAgentParam,
      allFeatures,
      selectedUngroupedFid,
      curProduct,
      updateAllFeaturesState,
      selectedArrayIndex,
    )

    console.log('re-render all feature end time: ', new Date().getTime());

    // updateZIndex(selectedCid, selectedFid, selectedLineItemName);

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  }, [
    allFeatures,
    selectedFid,
    selectedCid,
    selectedLineItemName,
    selectedTool,
    selectedAgentParam,
    selectedUngroupedFid,
    selectedArrayIndex,
  ]);

  useEffect(() => {
    selectedCidRef.current = selectedCid;
    selectedFidRef.current = selectedFid;
    selectedLineItemNameRef.current = selectedLineItemName;
    selectedUngroupedFidRef.current = selectedUngroupedFid;
    selectedAgentParamRef.current = selectedAgentParam;
    selectedArrayIndexRef.current = selectedArrayIndex;
  }, [
    selectedCid,
    selectedFid,
    selectedLineItemName,
    selectedUngroupedFid,
    selectedAgentParam,
    selectedArrayIndex,
  ]);

  useEffect(() => {
    if (_.isInteger(_.get(requiredLocateRect, 'fid'))) {
      const feature = _.find(displayedFeaturesRef.current, f => f.get('fid') === requiredLocateRect.fid);
      if (!feature) return;
      zoomPanToObject(feature, fcanvasRef.current);
      updateZIndex(selectedCid, selectedFid, selectedLineItemName, selectedUngroupedFid, selectedAgentParam);
    } else if (_.isInteger(_.get(requiredLocateRect, 'cid'))) {
      const component = _.find(displayedComponentBoxRef.current, c => c.get('gid') === requiredLocateRect.cid);
      if (!component) return;
      zoomPanToObject(component, fcanvasRef.current);
      updateZIndex(selectedCid, selectedFid, selectedLineItemName, selectedUngroupedFid, selectedAgentParam);
    }
    delayLoadHighSoluScene({
      fcanvasRef: fcanvasRef,
      rawImageW: _.get(curImageMetadata, 'width', 0),
      rawImageH: _.get(curImageMetadata, 'height', 0),
      displayedHighResolSceneRef: displayedHighResolSceneRef,
      imageUri: _.get(curProduct, 'inspectables[0].color_map_uri'),
      depthUri: _.get(curProduct, 'inspectables[0].depth_map_uri'),
    });
  }, [requiredLocateRect]);

  useEffect(() => {
    return () => {
      if (globalKeydownEventRef.current) {
        document.removeEventListener('keydown', globalKeydownEventRef.current);
      }
    }
  }, []);

  return (
    <Fragment>
      <AddComponentBySelectedFeatures
        isOpened={isAddComponentModalOpened}
        setIsOpened={setIsAddComponentModalOpened}
        allFeatures={allFeatures}
        curSelectedRectsRef={curSelectedRectsRef}
        curProduct={curProduct}
        refetchAllComponents={refetchAllComponents}
        refetchAllFeatures={refetchAllFeatures}
        updateAllFeaturesState={updateAllFeaturesState}
        fcanvasRef={fcanvasRef}
        allComponents={allComponents}
        refetchAllFeatureReevaluationResult={refetchAllFeatureReevaluationResult}
      />
      <AddComponentTemplate
        isOpened={isAddTemplateModalOpened}
        setIsOpened={setIsAddTemplateModalOpened}
        defaultPackageNo={templatePackageNo}
        defaultPartNo={templatePartNo}
        selectedArrayIndex={selectedArrayIndex}
        onConfirm={(pkgNo, pNo, selectedArrayIndex) => {
          handleAddComponentTemplate(pkgNo, pNo, selectedArrayIndex);
        }}
      />
      <div
        className='relative w-full h-full'
        ref={containerRef}
      >
        { !_.isEmpty(selectDimensionMousePos) &&
          <div
            className='absolute '
            style={{
              display: _.includes(['drawRegion', 'viewIn3D'], ) ? 'block' : 'none',
              top: `${selectDimensionMousePos.curMouseTop}px`,
              left: `${selectDimensionMousePos.curMouseLeft + 10}px`,
              borderRadius: '4px',
              background: '#56CCF2',
              zIndex: 11,
            }}
          >
            <div className='flex items-center gap-1 px-2'>
              <span
                className='font-source text-[12px] font-semibold'
                style={{ color: '#131313' }}
              >
                {`X: ${selectDimensionMousePos.curX}, Y: ${selectDimensionMousePos.curY}`}
              </span>
            </div>
          </div>
        }
        { !_.isEmpty(selectionOptionPos) &&
          <div
            className='absolute z-[20]'
            style={{
              display: _.isEmpty(selectionOptionPos) ? 'none' : 'inline-flex',
              top: `${selectionOptionPos.y + 5}px`,
              left: `${selectionOptionPos.x + 5}px`,
              borderRadius: '2px',
              background: '#1E1E1E',
              padding: '4px, 0',
              flexDirection: 'column',
              gap: '4px',
            }}
          >
            <div className='flex flex-col self-stretch p-1 gap-1 items-start'>
              { selectionOptionPos.type === 'component' &&
                <Fragment>
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);
                      if (!componentObj) return;
                      const relatedFeatures = _.filter(allFeatures, f => f.group_id === selectedCid);
                      if (!relatedFeatures) return;
                      handleSceneCopyComponentRect(
                        componentObj,
                        relatedFeatures,
                        addComponent,
                        addFeatureRoi,
                        t,
                        lazyGetAllFeatures,
                        curProduct,
                      ).then(({ cid, fids, newFeatureObjs }) => {
                        const run = async () => {
                          fcanvasRef.current.discardActiveObject().renderAll();
                          setSelectionOptionPos(null);
                          setSelectedCid(cid);
                          await refetchAllComponents();
                          await updateAllFeaturesState(fids, 'add', newFeatureObjs);
                          updateZIndex(cid, null, null, null, null);
                        };
                        run();
                      }).catch((e) => {
                        aoiAlert(t('notification.error.copyComponent'), ALERT_TYPES.COMMON_ERROR);
                        console.error(e);
                        return;
                      });
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.copyComponent')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);
                      if (!componentObj) return;
                      const features = _.filter(allFeatures, f => f.group_id === selectedCid);

                      handleSceneRemoveComponentOnly(
                        componentObj,
                        deleteComponent
                      ).then(() => {
                        const run = async () => {
                          setSelectionOptionPos(null);
                          setSelectedCid(null);
                          setSelectedArrayIndex(null);
                          await refetchAllComponents();
                          // await refetchAllFeatures();
                          await updateAllFeaturesState(_.map(features, f => f.feature_id), 'update', _.map(features, f => ({
                            ...f,
                            group_id: null,
                          })));
                        };
                        run();
                      }).catch((e) => {
                        aoiAlert(t('notification.error.deleteComponent'), ALERT_TYPES.COMMON_ERROR);
                        console.error(e);
                        return;
                      });
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.ungroupFeature')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);
                      if (!componentObj) return;
                      const relatedFeatures = _.filter(allFeatures, f => f.group_id === selectedCid && f.array_index === componentObj.array_index);
                      if (!relatedFeatures) return;

                      handleSceneRemoveComponentWithRelatedFeatures(
                        componentObj,
                        relatedFeatures,
                        deleteComponent,
                        deleteFeature,
                      ).then(({ fids }) => {
                        const run = async (fids) => {
                          setSelectedCid(null);
                          setSelectedArrayIndex(null);
                          setSelectionOptionPos(null);
                          await refetchAllComponents();
                          await updateAllFeaturesState(fids, 'delete');
                        };
                        run(fids);
                      }).catch((e) => {
                        aoiAlert(t('notification.error.deleteComponent'), ALERT_TYPES.COMMON_ERROR);
                        console.error(e);
                        return;
                      });
                    }}
                  >
                  <div className='flex items-center flex-1 justify-start gap-1'>
                    <span className='font-source text-[12px] font-normal leading-[150%]'>
                      {t('productDefine.removeComponentWithFeature')}
                    </span>
                  </div>
                </Button>
                { !_.isEmpty(_.get(_.find(allComponents, c => c.region_group_id === selectedCid), 'part_no', '')) &&
                  <Fragment>
                    <div className='w-full h-[1px] bg-[#333]' />
                    <Button
                      style={{ width: '100%' }}
                      type='text'
                      onClick={async () => {
                        const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid && c.array_index === selectedArrayIndex);
                        if (!componentObj) return;
                        if (_.isInteger(privateTemplateId)) {
                          const relatedFeatures = _.filter(allFeatures, f => f.group_id === selectedCid && f.array_index === selectedArrayIndex);
                          const canned_rois = _.map(relatedFeatures, f => ({
                            shape: {
                              type: 'obb',
                              points: [
                                { x: _.round(_.get(f, 'roi.points[0].x', 0) / mmToPixelRatio, 2), y: _.round(_.get(f, 'roi.points[0].y', 0) / mmToPixelRatio, 2) },
                                { x: _.round(_.get(f, 'roi.points[1].x', 0) / mmToPixelRatio, 2), y: _.round(_.get(f, 'roi.points[1].y', 0) / mmToPixelRatio, 2) },
                              ],
                              angle: _.get(f, 'roi.angle', 0),
                            },
                            checklist: _.get(f, 'line_item_params', {}),
                            feature_type: _.get(f, 'feature_type', ''),
                          }));
                          const centerPixel = getComponentCenterByRoiDtoObj(_.get(componentObj, 'shape', {}));

                          const additionalSearchTerms = buildSearchTerms([
                            _.get(componentObj, 'part_no', ''),
                            _.get(componentObj, 'package_no', ''),
                          ]);

                          const payload = {
                            builtin: false,
                            memo: '',
                            category: '',
                            package_no: _.get(componentObj, 'package_no', ''),
                            part_no: _.get(componentObj, 'part_no', ''),
                            additional_search_terms: additionalSearchTerms,
                            model: {
                              center: { x: _.round(centerPixel.x / mmToPixelRatio, 2), y: _.round(centerPixel.y / mmToPixelRatio, 2) },
                              canned_rois,
                              image_uri: _.get(componentObj, 'color_map_uri', ''),
                            },
                          };
                          const res = await updateComponentTemplate({ ...payload, id: privateTemplateId });
                          if (res.error) {
                            aoiAlert(t('notification.error.saveComponentTemplate'), ALERT_TYPES.COMMON_ERROR);
                            console.error(res.error.message);
                          } else {
                            aoiAlert(t('notification.success.saveComponentTemplate'), ALERT_TYPES.COMMON_SUCCESS);
                          }
                          setSelectionOptionPos(null);
                        } else {
                          setTemplatePackageNo(_.get(componentObj, 'package_no', ''));
                          setTemplatePartNo(_.get(componentObj, 'part_no', ''));
                          setComponentToSave(componentObj);
                          setIsAddTemplateModalOpened(true);
                        }
                      }}
                    >
                      <div className='flex items-center flex-1 justify-start gap-1'>
                        <span className='font-source text-[12px] font-normal leading-[150%]'>
                          { privateTemplateId ? t('productDefine.saveToPrivateLibrary') : t('productDefine.addToPrivateLibrary') }
                        </span>
                      </div>
                    </Button>
                  </Fragment>
                }
              </Fragment>
            }
              { selectionOptionPos.type === 'feature' &&
                <Fragment>
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedFid && f.group_id === selectedCid);
                      if (!featureObj) return;

                      const features = _.filter(allFeatures, f => f.group_id === selectedCid);
                      const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);

                      const run = async (
                        featureObj,
                        features,
                        componentObj,
                      ) => {
                        const res = await updateFeatureRoi({
                          body: {
                            ...featureObj,
                            roi: {
                              ...featureObj.roi,
                              angle: featureObj.roi.angle + 90,
                            },
                          },
                          params: { allComponents: true },
                        });

                        if (res.error) {
                          aoiAlert(t('notification.error.rotateFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res.error.message);
                          return;
                        }

                        const newComponentInfo = getComponentRectInfoByFeatures(
                          _.map(features, f => {
                            if (f.feature_id === featureObj.feature_id) {
                              return {
                                ...f,
                                roi: {
                                  ...f.roi,
                                  angle: f.roi.angle + 90,
                                }
                              }
                            }
                            return f;
                          }),
                          componentObj,
                        );

                        const cPayload = {
                          ...componentObj,
                          shape: {
                            ...componentObj.shape,
                            points: [
                              newComponentInfo.pMin,
                              newComponentInfo.pMax,
                            ],
                          },
                          // all: true,
                        };

                        delete cPayload['color_map_uri'];
                        delete cPayload['depth_map_uri'];
                        delete cPayload['created_at'];
                        delete cPayload['modified_at'];
                        delete cPayload['can_group_by_package_no'];
                        delete cPayload['can_group_by_part_no'];
                        delete cPayload['array_index'];
                        delete cPayload['cloned'];
                        delete cPayload['designator'];
                        delete cPayload['variation_for'];

                        const res1 = await updateComponent({
                          body: cPayload,
                          params: { allComponents: true },
                        });

                        if (res1.error) {
                          aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res1.error.message);
                          return;
                        }

                        setSelectionOptionPos(null);
                        await refetchAllComponents();
                        // await updateAllFeaturesState([featureObj.feature_id], 'update', [{
                        //   ...featureObj,
                        //   roi: {
                        //     ...featureObj.roi,
                        //     angle: featureObj.roi.angle + 90,
                        //   },
                        // }]);
                        if (!_.isEmpty(res.data)) {
                          await updateAllFeaturesState(_.map(res.data, 'feature_id'), updateFeatureInGroup, res.data);
                        }
                      };

                      run(featureObj, features, componentObj);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('productDefine.rotate90Right')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedFid && f.group_id === selectedCid);
                      if (!featureObj) return;

                      const features = _.filter(allFeatures, f => f.group_id === selectedCid);
                      const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);

                      const run = async (
                        featureObj,
                        features,
                        componentObj,
                      ) => {
                        const res = await updateFeatureRoi({
                          body: {
                            ...featureObj,
                            roi: {
                              ...featureObj.roi,
                              angle: featureObj.roi.angle - 90,
                            },
                          },
                          params: { allComponents: true },
                        });

                        if (res.error) {
                          aoiAlert(t('notification.error.rotateFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res.error.message);
                          return;
                        }

                        const newComponentInfo = getComponentRectInfoByFeatures(
                          _.map(features, f => {
                            if (f.feature_id === featureObj.feature_id) {
                              return {
                                ...f,
                                roi: {
                                  ...f.roi,
                                  angle: f.roi.angle - 90,
                                }
                              }
                            }
                            return f;
                          }),
                          componentObj,
                        );

                        const cPayload = {
                          ...componentObj,
                          shape: {
                            ...componentObj.shape,
                            points: [
                              newComponentInfo.pMin,
                              newComponentInfo.pMax,
                            ],
                          },
                          // all: true,
                        };

                        delete cPayload['color_map_uri'];
                        delete cPayload['depth_map_uri'];
                        delete cPayload['created_at'];
                        delete cPayload['modified_at'];
                        delete cPayload['can_group_by_package_no'];
                        delete cPayload['can_group_by_part_no'];
                        delete cPayload['array_index'];
                        delete cPayload['cloned'];
                        delete cPayload['designator'];
                        delete cPayload['variation_for'];

                        const res1 = await updateComponent({
                          body: cPayload,
                          params: { allComponents: true },
                        });

                        if (res1.error) {
                          aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res1.error.message);
                          return;
                        }

                        setSelectionOptionPos(null);
                        await refetchAllComponents();
                        if (!_.isEmpty(res.data)) {
                          await updateAllFeaturesState(_.map(res.data, 'feature_id'), updateFeatureInGroup, res.data);
                        }
                      };

                      run(featureObj, features, componentObj);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('productDefine.rotate90Left')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedFid && f.group_id === selectedCid);
                      if (!featureObj) return;

                      const features = _.filter(allFeatures, f => f.group_id === selectedCid);
                      const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);

                      const run = async (
                        featureObj,
                        features,
                        componentObj,
                      ) => {
                        const res = await updateFeatureRoi({
                          body: {
                            ...featureObj,
                            roi: {
                              ...featureObj.roi,
                              angle: featureObj.roi.angle + 180,
                            },
                          },
                          params: { allComponents: true },
                        });

                        if (res.error) {
                          aoiAlert(t('notification.error.rotateFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res.error.message);
                          return;
                        }

                        const newComponentInfo = getComponentRectInfoByFeatures(
                          _.map(features, f => {
                            if (f.feature_id === featureObj.feature_id) {
                              return {
                                ...f,
                                roi: {
                                  ...f.roi,
                                  angle: f.roi.angle + 180,
                                }
                              }
                            }
                            return f;
                          }),
                          componentObj,
                        );

                        const cPayload = {
                          ...componentObj,
                          shape: {
                            ...componentObj.shape,
                            points: [
                              newComponentInfo.pMin,
                              newComponentInfo.pMax,
                            ],
                          },
                          // all: true,
                        };

                        delete cPayload['color_map_uri'];
                        delete cPayload['depth_map_uri'];
                        delete cPayload['created_at'];
                        delete cPayload['modified_at'];
                        delete cPayload['can_group_by_package_no'];
                        delete cPayload['can_group_by_part_no'];
                        delete cPayload['array_index'];
                        delete cPayload['cloned'];
                        delete cPayload['designator'];
                        delete cPayload['variation_for'];

                        const res1 = await updateComponent({
                          body: cPayload,
                          params: { allComponents: true },
                        });

                        if (res1.error) {
                          aoiAlert(t('notification.error.updateComponent'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res1.error.message);
                          return;
                        }

                        setSelectionOptionPos(null);
                        await refetchAllComponents();
                        if (!_.isEmpty(res.data)) {
                          await updateAllFeaturesState(_.map(res.data, 'feature_id'), updateFeatureInGroup, res.data);
                        }
                      };

                      run(featureObj, features, componentObj);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('productDefine.rotate180')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  {/* <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);
                      if (!componentObj) return;
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedFid && f.group_id === selectedCid);
                      if (!featureObj) return;

                      handleSceneCopyFeatureIntoComponent(
                        componentObj,
                        featureObj,
                        addFeatureRoi,
                        updateComponent,
                        allFeatures,
                      ).then(({ fid, featureObj }) => {
                        const run = async () => {
                          setSelectionOptionPos(null);
                          await refetchAllComponents();
                          // await refetchAllFeatures();
                          await updateAllFeaturesState([fid], 'add', [featureObj]);
                          setSelectedFid(fid);
                        };
                        run();
                      }).catch((e) => {
                        console.error(e);
                        aoiAlert(t('notification.error.copyFeatureIntoComponent'), ALERT_TYPES.COMMON_ERROR);
                      });
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.copyFeatureIntoComponent')}
                      </span>
                    </div>
                  </Button> */}
                  {/* <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const componentObj = _.find(allComponents, c => c.region_group_id === selectedCid);
                      if (!componentObj) return;
                      const relatedFids = _.map(_.filter(allFeatures, f => f.group_id === selectedCid), 'feature_id');
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedFid && f.group_id === selectedCid);
                      if (!featureObj) return;

                      handleSceneRemoveFeatureFromComponent(
                        componentObj,
                        featureObj,
                        addComponent,
                        deleteComponent,
                        allFeatures,
                      ).then(({ cid }) => {
                        const run = async () => {
                          setSelectionOptionPos(null);
                          setSelectedFid(null);
                          await refetchAllComponents();
                          await updateAllFeaturesState(relatedFids, 'update', [{
                            ...featureObj,
                            group_id: null,
                          }]);
                          await sleep(100);
                          setSelectedCid(cid);
                        };
                        run();
                      }).catch((e) => {
                        aoiAlert(t('notification.error.removeFeatureFromComponent'), ALERT_TYPES.COMMON_ERROR);
                        console.error(e);
                        return;
                      });
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.removeFeatureFromComponent')}
                      </span>
                    </div>
                  </Button>
                  */}
                  {/* <div className='w-full h-[1px] bg-[#333]' /> */}
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedFid && f.group_id === selectedCid);
                      if (!featureObj) return;

                      handleSceneRemoveFeature(
                        featureObj,
                        deleteFeature,
                        allFeatures,
                        updateComponent,
                        allComponents,
                      ).then(({ fids }) => {
                        const run = async () => {
                          setSelectionOptionPos(null);
                          setSelectedFid(null);
                          await refetchAllComponents();
                          // await updateAllFeaturesState([featureObj.feature_id], 'delete');
                          await updateAllFeaturesState(fids, 'delete');
                        };
                        run();
                      }).catch((e) => {
                        aoiAlert(t('notification.error.deleteFeature'), ALERT_TYPES.COMMON_ERROR);
                        console.error(e);
                        return;
                      });
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.remove')}
                      </span>
                    </div>
                  </Button>
                </Fragment>
              }
              { selectionOptionPos.type === 'featureGroup' &&
              // ungrouped features
                <Fragment>
                  {curSelectedRectsRef.current.length === 1 &&
                  <Fragment>
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedUngroupedFid);
                      if (!featureObj) return;

                      const run = async (
                        featureObj,
                      ) => {
                        const res = await updateFeatureRoi({
                          body: {
                            ...featureObj,
                            roi: {
                              ...featureObj.roi,
                              angle: featureObj.roi.angle + 90,
                            },
                          },
                          params: { allComponents: _.isInteger(featureObj.group_id) },
                        });

                        if (res.error) {
                          aoiAlert(t('notification.error.rotateFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res.error.message);
                          return;
                        }

                        setSelectionOptionPos(null);
                        // await updateAllFeaturesState([featureObj.feature_id], 'update', [{
                        //   ...featureObj,
                        //   roi: {
                        //     ...featureObj.roi,
                        //     angle: featureObj.roi.angle + 90,
                        //   },
                        // }]);
                        if (!_.isEmpty(res.data)) {
                          await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
                        }
                      };

                      run(featureObj);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('productDefine.rotate90Right')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedUngroupedFid);
                      if (!featureObj) return;

                      const run = async (
                        featureObj,
                      ) => {
                        const res = await updateFeatureRoi({
                          body: {
                            ...featureObj,
                            roi: {
                              ...featureObj.roi,
                              angle: featureObj.roi.angle - 90,
                            },
                          },
                          params: { allComponents: _.isInteger(featureObj.group_id) },
                        });

                        if (res.error) {
                          aoiAlert(t('notification.error.rotateFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res.error.message);
                          return;
                        }

                        setSelectionOptionPos(null);
                        // await updateAllFeaturesState([featureObj.feature_id], 'update', [{
                        //   ...featureObj,
                        //   roi: {
                        //     ...featureObj.roi,
                        //     angle: featureObj.roi.angle - 90,
                        //   },
                        // }]);
                        if (!_.isEmpty(res.data)) {
                          await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
                        }
                      };

                      run(featureObj);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('productDefine.rotate90Left')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const featureObj = _.find(allFeatures, f => f.feature_id === selectedUngroupedFid);
                      if (!featureObj) return;

                      const run = async (
                        featureObj,
                      ) => {
                        const res = await updateFeatureRoi({
                          body: {
                            ...featureObj,
                            roi: {
                              ...featureObj.roi,
                              angle: featureObj.roi.angle + 180,
                            },
                          },
                          params: { allComponents: _.isInteger(featureObj.group_id) },
                        });

                        if (res.error) {
                          aoiAlert(t('notification.error.rotateFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error(res.error.message);
                          return;
                        }

                        setSelectionOptionPos(null);
                        // await updateAllFeaturesState([featureObj.feature_id], 'update', [{
                        //   ...featureObj,
                        //   roi: {
                        //     ...featureObj.roi,
                        //     angle: featureObj.roi.angle + 180,
                        //   },
                        // }]);
                        if (!_.isEmpty(res.data)) {
                          await updateAllFeaturesState(_.map(res.data, 'feature_id'), 'update', res.data);
                        }
                      };

                      run(featureObj);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('productDefine.rotate180')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  </Fragment>
                  }
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      setIsAddComponentModalOpened(true);
                      setSelectionOptionPos(null);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('productDefine.groupSelectedFeatures')}
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const featureObjs = _.map(curSelectedRectsRef.current, r => r.get('featureObj'));

                      const run = async (srcFeatures) => {
                        handleSceneCopyBatchUngroupedFeatures(srcFeatures, addFeatureRoi)
                        .then(({ newFIds, newFeatureObjs }) => {
                          // this is esstentially when multiple rects are selected
                          fcanvasRef.current.discardActiveObject().renderAll();
                          updateAllFeaturesState(newFIds, 'add', newFeatureObjs);
                          // const newFeatureRects = _.find(displayedFeaturesRef.current, f => _.includes(newFIds, f.get('fid')));
                          // if (!newFeatureRects) return;
                          setSelectionOptionPos(null);
                          setSelectedUngroupedFid(null);
                        }).catch((e) => {
                          aoiAlert(t('notification.error.copyFeature'), ALERT_TYPES.COMMON_ERROR);
                          console.error(e);
                        });
                      };

                      run(featureObjs);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.copy')}({curSelectedRectsRef.current.length})
                      </span>
                    </div>
                  </Button>
                  <div className='w-full h-[1px] bg-[#333]' />
                  <Button
                    style={{ width: '100%' }}
                    type='text'
                    onClick={() => {
                      const run = async (curSelectedRects) => {
                        dispatch(setIsContainerLvlLoadingEnabled(true));

                        for (const fRect of curSelectedRects) {
                          const featureObj = fRect.get('featureObj');
                          const res = await deleteFeature({
                            product_id: featureObj.product_id,
                            step: featureObj.step,
                            feature_id: featureObj.feature_id,
                          });

                          if (res.error) {
                            console.error('deleteFeature error:', _.get(res, 'error.message', ''));
                            aoiAlert(t('notification.error.deleteFeature'), ALERT_TYPES.COMMON_ERROR);
                            dispatch(setIsContainerLvlLoadingEnabled(false));
                            return;
                          }
                        }

                        setSelectionOptionPos(null);

                        if (!_.isEmpty(curSelectedRects)) {
                          fcanvasRef.current.discardActiveObject().renderAll();
                        }

                        await updateAllFeaturesState(_.map(curSelectedRects, f => f.get('fid')), 'delete');
                        dispatch(setIsContainerLvlLoadingEnabled(false));
                      };

                      run(curSelectedRectsRef.current);
                    }}
                  >
                    <div className='flex items-center flex-1 justify-start gap-1'>
                      <span className='font-source text-[12px] font-normal leading-[150%]'>
                        {t('common.delete')}({curSelectedRectsRef.current.length})
                      </span>
                    </div>
                  </Button>
                </Fragment>
              }
            </div>
          </div>
        }
        <div
          className='absolute top-0 left-0 w-full h-full z-[10]'
          ref={viewerContainerRef}
        >
          <canvas
            ref={canvasElRef}
            onContextMenu={(e) => {
              e.preventDefault();
            }}
          />
        </div>
      </div>
    </Fragment>
  );
};

export default TemplateEditorViewer;