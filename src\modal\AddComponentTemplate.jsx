import React, { useEffect, useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { Button, Input } from 'antd';
import { useTranslation } from 'react-i18next';

const AddComponentTemplate = (props) => {
  const {
    isOpened,
    setIsOpened,
    defaultPackageNo,
    defaultPartNo,
    onConfirm,
    selectedArrayIndex,
  } = props;

  const { t } = useTranslation();

  const [packageNo, setPackageNo] = useState(defaultPackageNo || '');
  const [partNo, setPartNo] = useState(defaultPartNo || '');

  useEffect(() => {
    if (isOpened) {
      setPackageNo(defaultPackageNo || '');
      setPartNo(defaultPartNo || '');
    }
  }, [isOpened, defaultPackageNo, defaultPartNo]);

  return (
    <CustomModal
      width={386}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[150%] whitespace-nowrap'>
        {t('productDefine.addToPrivateLibrary')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col self-stretch'>
        <div className='flex py-6 px-4 flex-col gap-8 self-stretch'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
              {t('productDefine.packageNo')}
            </span>
            <Input
              size='small'
              style={{ width: '100%' }}
              value={packageNo}
              onChange={(e) => setPackageNo(e.target.value)}
            />
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[12px] font-normal leading-[150%] whitespace-nowrap'>
              {t('productDefine.partNo')}
            </span>
            <Input
              size='small'
              style={{ width: '100%' }}
              value={partNo}
              onChange={(e) => setPartNo(e.target.value)}
            />
          </div>
        </div>
        <div className='flex p-4 gap-2 items-center flex-1 self-stretch'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            type='primary'
            style={{ width: '50%' }}
            onClick={() => {
              onConfirm(packageNo, partNo, selectedArrayIndex);
            }}
          >
            <span className='font-source text-[12px] font-semibold leading-[150%]'>
              {t('productDefine.addToPrivateLibrary')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  );
};

export default AddComponentTemplate;
